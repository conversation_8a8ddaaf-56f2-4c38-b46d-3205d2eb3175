/*
 * @Author: lucyfang
 * @Date: 2024-12-09 10:38:07
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-05-26 15:25:05
 * @Description: 请输入注释信息
 */
import { ReportDataMsgType } from './base';

export interface LinkMsgDataType {
  href?: string;
}

export interface RouteDataMsgType {
  from: string;
  to: string;
}

export interface RouteMsgType extends ReportDataMsgType, RouteDataMsgType {}

// 通用上报结构
export interface IAnyMsgType extends ReportDataMsgType {
  [key: string]: any;
}
