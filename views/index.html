<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link
      rel="shortcut icon"
      type="text/css"
      href="<%= `/imgcache/common/img/logo_favicon.ico` %>"
    />
    <title>CSIG极光</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="jiguang-platform" />
    <link
      rel="stylesheet"
      type="text/css"
      href="<%= `/imgcache/common/platform/pro-components.min.css` %>"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="<%= `/imgcache/common/platform/logic-flow.min.css` %>"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="<%= `/imgcache/common/platform/logic-flow.extension.min.css` %>"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="<%= `/imgcache/common/platform/tea.min.css` %>"
    />
    <link
      href="/imgcache/common/platform/tea-theme-1.1.0-beta.23.css"
      rel="stylesheet"
      data-role="global"
    />

    <link
      rel="stylesheet"
      type="text/css"
      href="<%= `/imgcache/tcs-component/css/tcs-component.min.css` %>"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="<%= `/imgcache/common/layout/css/layout.css` %>"
    />
    <style>
      .ant-table-tbody > tr.ant-table-row:hover > td {
        background: #e6f7ff !important;
      }
      body {
        margin: 0 i !important;
        color: rgba(0, 0, 0, 0.85) !important;
        font-size: 14px !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
          'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji',
          'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji' !important;
        font-variant: tabular-nums !important;
        line-height: 1.5715 !important;
        background-color: #fff !important;
        -webkit-font-feature-settings: 'tnum', 'tnum' !important;
        font-feature-settings: 'tnum', 'tnum' !important;
      }
    </style>
    <script type="text/javascript">
      window.CDSC_RTX = <%- JSON.stringify(username) %>;
    </script>
    <script src="<%= `//pulse.woa.com/wt/js/wm.js` %>"></script>
    <script type="text/javascript">
      window['jiguang_username'] =<%- JSON.stringify(username) %>
      window["jiguang_domain"]="<%=domain%>"
    </script>
    <!-- <script src="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js?_bid=3977"></script>
    <link
      rel="preload"
      href="https://cdn-go.cn/aegis/aegis-sdk/latest/aegis.min.js?_bid=3977"
      as="script"
    />-->
  </head>

  <body class="jiguang-platform">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root-layout">
      <div
        style="text-align: center; background: #f0f2f5; width: 100vw; height: 100vh"
      >
        欢迎使用TCE极光平台,请稍安勿躁! 系统正在努力加载中......
      </div>
    </div>

    <script type="text/javascript">
      window["jiguang_api_domain"] = "<%=apiDomain%>"
      window["jiguang_capi_domain"] = window.localStorage.getItem('JIGUANG_ENV')||"<%=capiDomain%>"
      window['jiguang_dataRight'] =<%- JSON.stringify(dataRight) %>
      window['jiguang_menus']=<%- JSON.stringify(menus) %>
      window['jiguang_currentRole']=<%- JSON.stringify(currentRole) %>
      window['jiguang_headerNavApp']=<%- JSON.stringify(headerNavApp) %>
      window['jiguang_headerRoleInfo']=<%- JSON.stringify(headerRoleInfo) %>

      var _JIGUANG_APP_TOKEN =<%- JSON.stringify(token) %>
      Object.keys(_JIGUANG_APP_TOKEN).forEach(key => {
          window.localStorage.setItem(key, _JIGUANG_APP_TOKEN[key])
      })

      /* 处理多域名*/
      if (window.jiguang_domain !== window.location.host) {
          window.jiguang_api_domain = window.jiguang_api_domain.replace(window.jiguang_domain, window.location.host);
          window.jiguang_capi_domain = window.jiguang_capi_domain.replace(window.jiguang_domain, window.location.host);
          window.jiguang_domain = window.location.host;
      }
      /*external handle*/
      window["jiguang_authSource"] = "<%=authSource%>"
      window["jiguang_external_user"] = <%- JSON.stringify(externalUsers) %>

      /**记录空间数据**/
      window["jiguang_headerNsList"]=<%- JSON.stringify(headerNsList)%>
      window["jiguang_currentNs"] = "<%=currentNs%>"
    </script>
    <script
      type="text/javascript"
      src="<%= `/imgcache/common/platform/react.production.min.js` %>"
    ></script>
    <script
      type="text/javascript"
      src="<%= `/imgcache/common/platform/react-dom.production.min.js` %>"
    ></script>
    <script
      type="text/javascript"
      src="<%= `/imgcache/common/platform/react-router.min.js` %>"
    ></script>
    <script
      type="text/javascript"
      src="<%= `/imgcache/common/platform/react-router-dom.min.js` %>"
    ></script>
    <script
      type="text/javascript"
      src="<%= `/imgcache/common/platform/moment.min.js` %>"
    ></script>
    <!-- <script
      type="text/javascript"
      src="<%= `/imgcache/common/platform/antd.min.js` %>"
    ></script>
    <script
      type="text/javascript"
      src="<%= `/imgcache/common/platform/pro-components.min.js` %>"
    ></script> -->

    <script src="/imgcache/common/platform/prop-types.js" crossorigin></script>
    <script src="/imgcache/common/platform/state-local.min.js" crossorigin></script>
    <script
      src="/imgcache/common/platform/monaco-loader.min.js"
      crossorigin
    ></script>
    <script src="/imgcache/common/platform/monaco-react.min.js" crossorigin></script>

    <script src="/imgcache/common/platform/logic-flow.min.js" crossorigin></script>

    <script src="/imgcache/common/platform/exceljs.min.js" crossorigin></script>
    <script src="/imgcache/common/platform/lodash.min.js" crossorigin></script>
    <script
      src="/imgcache/common/platform/logic-flow.Menu.min.js"
      crossorigin
    ></script>
    <script
      type="text/javascript"
      src="<%= `/imgcache/common/platform/axios.min.js` %>"
    ></script>
    <script
      type="text/javascript"
      src="<%= `/imgcache/common/platform/localforage.min.js` %>"
    ></script>
    <script
      type="text/javascript"
      src="<%= `/imgcache/common/platform/tea-component.min.js` %>"
    ></script>

    <script
      type="text/javascript"
      src="<%= `/imgcache/tcs-component/js/tcs-component.min.js` %>"
    ></script>

    <script
      type="text/javascript"
      src="<%= `/imgcache/tcsc-base/js/tcsc-base.min.js` %>"
    ></script>

    <script
      type="text/javascript"
      src="<%= `/imgcache/common/layout/js/layout.js` %>"
    ></script>

    <script
      type="text/javascript"
      src='<%- authSource?"":"/api/v1/allUsers"%>'
    ></script>

    <!-- <script
    type="text/javascript"
    src="/api/v1/allUsers"
  ></script> -->
    <script
      type="text/javascript"
      src="<%= `/imgcache/common/platform/platform.js` %>"
    ></script>
    <script type="text/javascript">
      window.addEventListener('load', function () {
        window['allMemberList'] = (window['_arrusers'] || []).map(function (
          rtxArray
        ) {
          return { enName: rtxArray[0], combineName: rtxArray[1] };
        });
        if (window['jiguang_external_user']) {
          window['allMemberList'] = window['allMemberList'].concat(
            (window['jiguang_external_user'] || []).map((x) => ({
              enName: x,
              combineName: x,
            }))
          );
        }
      });
    </script>
    <script
      type="text/javascript"
      src="<%= `/imgcache/common/platform/heimdallr-browser/page_crash.umd.js` %>"
    ></script>
    <script
      type="text/javascript"
      src="<%= `/imgcache/common/platform/heimdallr-browser/hash.umd.js` %>"
    ></script>
    <script
      type="text/javascript"
      src="<%= `/imgcache/common/platform/heimdallr-browser/fetch.umd.js` %>"
    ></script>
    <script
      type="text/javascript"
      src="<%= `/imgcache/common/platform/heimdallr-browser/dom.umd.js` %>"
    ></script>
    <script
      type="text/javascript"
      src="<%= `/imgcache/common/platform/heimdallr-browser/customer.umd.js` %>"
    ></script>
    <script
      type="text/javascript"
      src="<%= `/imgcache/common/platform/heimdallr-browser/browser.iife.js` %>"
    ></script>

    <!-- <link
      rel="stylesheet"
      type="text/css"
      href="<%= `/imgcache/common/platform/antd.min.css` %>"
    /> -->
  </body>
</html>
