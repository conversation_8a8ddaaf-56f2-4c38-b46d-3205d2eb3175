import React, { useState, useMemo, useRef } from 'react';
import { Button, H1, Layout, Stepper, message } from '@tencent/tea-component';
import { TcsSpace, TcsModal } from '@tencent/tcs-component';
// import AppTable from '../../AccessItems/AppList/AppTable';
import AppTable from '../AppList/AppTable';
import { IPropsIssueSolutionRel } from '@/common/api/interationManage';
import DependAssessModal, { DependAssessModalRef } from '../DependAssessModal';
import AppListMr from '../AppListMr';
import AddRelRegionOnlyContent from '../AddRelRegionOnlyContent';
import OperationTools from '../OperationTools';
import VerifyExport from '../VerifyExport';
import { IterationManage, evaluateDependencies } from '@/common/api/iterationManage.api';
import { getUrlParams } from '@/common/utils';
const { Body, Sider, Content } = Layout;

export interface IProps {
  pagePath: 'story' | 'defect';
  onReload?: () => void;
  IssueID: string;
  onLoading?: (status: boolean) => void;
  tableDataSource?: IPropsIssueSolutionRel[];
  isManager: boolean;
  tapdInfo: any;
}

interface Step {
  id: string;
  label: string;
  component?: any; // 改为 any 类型，更灵活，可以为 null
  props?: any;
}

const Operation: React.FC<IProps> = ({ pagePath, IssueID, isManager, tapdInfo }) => {
  const urlParams = getUrlParams();
  // 提取公共变量，避免重复定义
  const { workspace_id: workspaceId, obj_type: objType, obj_id: objId } = urlParams;
  const [record, setRecord] = useState<any>({});
  const [dependencyData, setDependencyData] = useState<any>(null); // 存储依赖评估数据
  const [loading, setLoading] = useState(false);
  const dependAssessModalRef = useRef<DependAssessModalRef>(null);
  const tapdUrl = useMemo(
    () => `http://tapd.oa.com/${workspaceId}/${objType === 'bug' ? 'bugtrace/bugs' : 'prong/stories'}/view/${objId}`,
    [workspaceId, objType, objId],
  );
  const handleDetail = async () => {
    try {
      console.log('调用 ListIssueSolutionRelsWithDetail 获取 record 数据:', { tapdUrl });

      const res = await IterationManage.ListIssueSolutionRelsWithDetail({
        TapdUrl: tapdUrl,
      });

      console.log('ListIssueSolutionRelsWithDetail 返回数据:', res);

      if (res?.data?.[0]) {
        const recordData = res.data[0];
        setRecord(recordData);
        console.log('设置 record:', recordData);
        return recordData;
      }
      console.warn('未获取到 record 数据');
      return null;
    } catch (error) {
      console.error('获取 record 数据失败:', error);
      return null;
    }
  };

  // 依赖评估确认函数，使用 DependAssessModal 组件中的数据
  const handleDependencyConfirm = async () => {
    try {
      setLoading(true);
      console.log('执行依赖评估确认操作');

      // 从 DependAssessModal 组件获取确认数据
      const confirmData = dependAssessModalRef.current?.getConfirmData();

      if (!confirmData) {
        console.log('没有依赖关系数据，直接继续');
        return true;
      }

      console.log('依赖评估请求数据:', confirmData);

      const res = await evaluateDependencies(confirmData);
      if (res?.Data?.Message) {
        message.error({ content: res.Data.Message });
        return false;
      }
      console.log('依赖评估确认成功');
      setDependencyData(confirmData);
      message.success({ content: '依赖评估确认成功' });
      return true;
    } catch (error) {
      console.error('依赖评估确认失败:', error);
      message.error({ content: '依赖评估确认失败，请重试' });
      return false;
    } finally {
      setLoading(false);
    }
  };
  // 动态生成 steps，根据解决方案版本决定是否包含 evaluateDependencies 步骤
  const steps: Step[] = useMemo(() => {
    // 检查是否需要依赖评估步骤
    const solutionVersion = tapdInfo?.SolutionVersion;
    const needDependencyEvaluation = solutionVersion === 'TCE3.10.0' || solutionVersion === 'TCE3.10.11';

    console.log('解决方案版本:', solutionVersion, '是否需要依赖评估:', needDependencyEvaluation);

    const baseSteps = [
      {
        id: 'evaluateApp',
        label: '评估应用',
        component: AppTable as any,
        props: {
          isManager,
          pagePath,
          onReload: () => {},
          IssueID,
          onLoading: () => {},
          tapdInfo,
        },
      },
    ];

    // 如果需要依赖评估，添加 evaluateDependencies 步骤
    if (needDependencyEvaluation) {
      baseSteps.push({
        id: 'evaluateDependencies',
        label: '评估依赖关系',
        component: DependAssessModal as any, // 特殊处理，在渲染时单独处理
        props: {
          isManager,
          pagePath,
          onReload: () => {},
          IssueID,
          onLoading: () => {},
          tapdInfo,
          record,
        } as any,
      });
    }

    // 添加其余步骤
    baseSteps.push(
      {
        id: 'mergeApp',
        label: '合流应用',
        component: AppListMr as any,
        props: {
          isManager,
          pagePath,
          onReload: () => {},
          IssueID,
          onLoading: () => {},
          tapdInfo,
          record,
        } as any,
      },
      {
        id: 'evaluateSite',
        label: '评估局点',
        component: AddRelRegionOnlyContent as any,
        props: {
          isManager,
          pagePath,
          onReload: () => {},
          IssueID,
          onLoading: () => {},
          tapdInfo,
        },
      },
      {
        id: 'createChangeOrder',
        label: '制作变更单',
        component: OperationTools as any,
        props: {
          isManager,
          pagePath,
          onReload: () => {},
          IssueID,
          onLoading: () => {},
          tapdInfo,
        },
      },
      {
        id: 'fillConclusion',
        label: '填写验证结论',
        component: VerifyExport as any,
        props: {
          isManager,
          pagePath,
          onReload: () => {},
          IssueID,
          onLoading: () => {},
          tapdInfo,
          record,
        } as any,
      },
    );

    return baseSteps;
  }, [isManager, pagePath, IssueID, tapdInfo, record]);

  const [currentStep, setCurrentStep] = useState(0);
  const [tapdStatus, setTapdStatus] = useState('待评估修复');

  const handleNext = async () => {
    if (currentStep < steps.length - 1) {
      const currentStepId = steps[currentStep].id;
      const nextStepId = steps[currentStep + 1].id;

      // 从 evaluateApp 步骤切换到下一步时，如果下一步是 evaluateDependencies，调用 handleDetail 接口
      if (currentStepId === 'evaluateApp' && nextStepId === 'evaluateDependencies') {
        console.log('从评估应用切换到评估依赖关系，调用 handleDetail 接口');
        await handleDetail();
      }

      // 从 evaluateDependencies 步骤切换到 mergeApp 步骤时，执行依赖评估确认操作
      if (currentStepId === 'evaluateDependencies' && nextStepId === 'mergeApp') {
        console.log('从评估依赖关系切换到合流应用，执行依赖评估确认操作');
        const confirmResult = await handleDependencyConfirm();
        if (!confirmResult) {
          // 如果确认失败，不进行步骤切换
          return;
        }
      }

      // 从 evaluateApp 直接切换到 mergeApp（跳过 evaluateDependencies）时，也需要调用 handleDetail
      if (currentStepId === 'evaluateApp' && nextStepId === 'mergeApp') {
        console.log('从评估应用直接切换到合流应用，调用 handleDetail 接口');
        await handleDetail();
      }

      if (currentStepId === 'evaluateSite' && tapdStatus === '待评估修复') {
        TcsModal.warning({
          title: '提示',
          content: '当前TAPD单状态为待评估修复，请先扭转状态至已评估待合流，再进行操作。',
        });
        setTapdStatus('已评估待合流');
        return;
      }
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <Layout style={{ height: 720 }}>
      <Body>
        <Sider style={{ width: '150px' }}>
          <Stepper style={{ marginTop: 10 }} type="vertical" steps={steps} current={steps[currentStep].id} />
        </Sider>
        <Content>
          <Content.Body full>
            {(() => {
              if (!steps[currentStep].component) {
                return <H1>{steps[currentStep].label}</H1>;
              }

              // 特殊处理 evaluateDependencies 步骤，使用 ref
              if (steps[currentStep].id === 'evaluateDependencies') {
                return <DependAssessModal ref={dependAssessModalRef} issueID={IssueID} record={record} />;
              }

              // 其他步骤正常渲染
              return React.createElement(steps[currentStep].component, steps[currentStep].props || {});
            })()}
          </Content.Body>
        </Content>
      </Body>
      <Layout.Footer style={{ height: 40, marginTop: 10, textAlign: 'center' }}>
        <TcsSpace>
          <Button>暂存</Button>
          <Button onClick={handlePrev}>上一步</Button>
          <Button onClick={handleNext} loading={loading}>
            下一步
          </Button>
        </TcsSpace>
      </Layout.Footer>
    </Layout>
  );
};

export default Operation;
