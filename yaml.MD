values.yaml文件中字段的填写规则

字段code

是否必选

数据类型

校验规则

字段说明

备注

tad.namespace

必选字段

字符串

不能为空

指明应用所在的命名空间

tad.images.$component.url

必选字段

自动注入字段

不需校验

应用下组件的镜像地址

tad.images.$component.tag

可选字段

字符串

不需校验

应用下组件镜像tag

tad.images.$component.version

可选字段

对象

不需校验

应用下组件镜像的版本，当一个镜像对应多个版本时通过version区分

tad.registries

可选字段

字符串

不需校验

自定义镜像仓库地址

tad.packages

可选字段

对象

不需校验

非镜像接入，以RPM/DEB/RAW格式接入文件的存放地址

tad.services.definitions

可选字段

对象

不需校验

应用对外提供的服务信息

使用TED规范接入的产品，这里按照兼容TED的标准，TAD规范下组件暴露和依赖的服务在application.yaml中声明，这里不需要填写。TCE3.10.0下产品需要使用该字段，TCE3.10.11和PAAS解决放下不需要该字段

tad.services.dependencies

可选字段

对象

不需校验

应用依赖的服务

tad.components.$component.type

必选字段

枚举

填写的值必须为以下字段之一，不用区分大小写：Deployment、StatefulSet、MiddlewareWorkload、NonContainerJob、NonContainerWorkload、Job、ServiceInit、PresetInit、DaemonSet

组件的类型

tad.components.$component.nodes

可选字段

对象

不需校验

生产组件待部署的服务器组IP地址，极光和产品市场都不消费，只有在极光CD环境部署时手工制定生产节点时使用。项目部署时，产品市场能够根据规划包中生产组件id和对应的服务ID算出服务器组IP地址

tad.components.$component.images

可选字段

字符串

不需校验

容器组件镜像地址

tad.components.$component.services

可选字段

对象

不需校验

声明组件的service网络属性，比如服务类型(NodePort、ClusterIP)、端口号等

tad.components.$component.replicas

可选字段

数字

必须为正整数，不能置空。

当组件类型为Deployment、StatefulSet时必须填写，其他组件类型不需要填写。

组件副本的数量

tad.components.$component.resources

可选字段

对象

如果填写该字段，规则：1、resources.$container.requests下资源的值<= resources.$container.limits下资源的值;2.cpu的单位是“m”，memory和strage 单位要么是“Mi”要么是“M”,要么是“Gi”要么是“G”（注意单位换算，注意大小写。当cpu不带单位时，表示单位是10^3m）

组件下容器的资源规格说明

cpu的单位是“m”，memory和strage 单位要么是“Mi”要么是“M”,要么是“Gi”要么是“G”

tad.components.$component.scheduling

可选字段

对象

不需校验

描述组件的部署需求，包含node亲和/反亲和，pod亲和/反亲和

tad.components.$component.probe

可选字段

对象

不需校验

组件的健康检查探针，设定就绪检查和健康检查延迟执行的时间

tad.components.$component.pdb

可选字段（有特殊场景）

对象

对于component.type为"deployment"、"statefulset"类型的组件必须是必填，且minAvailable和maxUnavailable字段至少存在一个

对象包含minAvailable和maxUnavailable两个字段，分别表示组件正常服务前提下，需要的最小可用数或最大可用数，两个选填一个即可

填写样例1，设置最小可副本数：

  pdb:

    minAvailable: 1

填写样例2，设置最大不可用副本数：

  pdb: 

    maxUnavailable: 2

tad.components.$component.parameters.binding.extras

可选字段（有特殊场景）

对象

当tad.components.$component.type: middleware或middlewareWrokload(不区分大小写)，

且tad.components.$component.parameters.instanceType: planning时，tad.components.$component.parameters.binding.extras下面必须要有2个字段：

__scope__、

__serviceID__

tad.components.$component.parameters.binding.extras.__scope__表示中间件的服务级别；

tad.components.$component.parameters.binding.extras.__serviceID__表示中间件注册到pajero中的服务ID,其他应用通过这个服务ID查询访问该服务的信息。

    

tad:
  components:
    demo-comp:
      type: MiddlewareWorkload
      parameters:
        instanceType: planning
        binding:
          extras:
            __service_id__: demo-comp-serviceID
            __scope__ : region
        class: etcd
        instanceRef:
          name: demo
      

tad.configs

可选字段

对象

不需校验

为兼容TED规范而保留下来的字段，用以承载TED规范下的服务级别、私有配置等信息

tad.global

可选字段

对象

不需校验

应用部署的全局配置信息，项目交付时，极光会输出全局配置并覆盖应用的全局配置

tad.clusterScope.level

必填字段

枚举

可供填写的值为：global, cluster

表示应用的部署策略，global表示当有多个tcs集群时，该应用只部署一个，是默认策略。cluster表示每个tcs集群都要部署一个。

tad.clusterScope.selecter

可选字段

字符串

不需校验

集群标签选择

tad.instanceStrategy.level

必填字段

枚举

可供填写的值为:idc、zone, region, global

声明应用的服务级别

tad.instanceStrategy.multiInstances

必填字段

布尔

布尔值校验规则

true表示在一个tcs集群中可以部署多个实例，false表示一个tcs集群中只能部署一个实例

xxx.xxx

可选字段

对象

不需校验

tad规范下定义的私有规范，和tad保持同级，用以存放应用的私有配置，如应用某项特殊功能的部署开关等

例如：selfConfig.deployMode

组件values填写样例

values.yaml中填写的字段是应用于暴露应用部署相关的配置，values.yaml文件在项目规划和部署阶段都是可以修改的，而应用application.yaml和应用制品在研发流水线构建完成后就不可更改，所以values.yaml中的字段必须要填写正确，并在application.yaml正确的应用到对应的字段。

tad.namespace和tad.$组件名.scheduling下的配置不用在application.yaml中显式引用，应用部署时tad能够自动将这些字段合并到application.yaml。

tad.global在研发阶段不用填写，这个字段会由全局配置提供，当应用在沙箱环境做部署验证时，由沙箱环境维护的全局配置提供global配置，当应用做项目交付时，由极光交付规划生成的全局配置提供global配置。若在应用的values.yaml中填写了tad.global字段，则应用application.yaml中引用到tad.global.xx相关的字段最终会使用沙箱环境或极光交付规划生产的全局配置，不会使用应用values.yaml中的tad.global配置。

deployment、deployment、statfulset等类型的组件统称为工作负载(workload),包含该类型的应用的values.yaml下必须有tad.components字段。config、preset、yunapi、dbsql等类型的组件属于配置项，若应用下只包含有配置项组件，则应用values.yaml中不用写components字段。

MiddleWorkload填写样例

# values.yaml
tad:
  namespace: tce
  components:
    # 实例名称 -- 对应研发中心中间件规格参数中的实例名称
    tad-middleware-demo-dbsql:
      # 注意事项1 ： 必须声明为 middleware 类型
      type: MiddlewareWorkload
      # 业务参数
      parameters:
        # 中间件类型 -- 对应研发中心中间件工作负载中的细分类型
        class: mysql
        version: 5.7
        # 中间件数据需求
        binding:
          databases:
              - ccdb1
              - ccdb4
        # 业务容量参数
        resources:
          storage: 100Gi
          cpu: 4000m
          memory: 8Gi 
        # 共享 ID 如果多个应用的想要使用同一个1个中间件实例，则需要声明 shareID 一致 
        # 建议 {产品名称}-{产品名称}-[应用名称]-[应用名称]-[..]，避免重复
        # 期望是每个产品中的多个应用使用一个实例，所在shareID 是必须填写
        shareID: msyql.db-2
        # 实例类型：planning（待规划）
        instanceType: planning
        # 当有初始值，但 instanceType 是 planning 时，规划注入覆盖
        instanceRef:  
          # 中间件实例名称，instanceType 是 planning 时，由规划注入
          name: db-2

nonContainerJob填写样例

组件下的labels、scheduling为非必填，在产品交付方案建模阶段也可以通过公式写入规则。

注意：nonContainerJob的podAntiAffinity topologyKey标签为 planning.aurora.tce.io/hostname ，而deployment 的podAntiAffinity topologyKey标签为 kubernetes.io/hostname

tad:
  components:
    product-cbs-snap:
      nodes:
        cbs-snapshot-capserver:
        - ********
        - ********
      labels:
        infra.tce.io/oam-app: product-cbs-snap
        infra.tce.io/oam-comp: cbs-snapshot-capserver
      replicas: 3
      scheduling:
        topologySpreadConstraints:
        - labelSelector:
            matchLabels:
                infra.tce.io/oam-comp: cbs-snapshot-capserver
            maxSkew: 1
            topologyKey: infra.tce.io/zone
            whenUnsatisfiable: DoNotSchedule
        affinity:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
              - matchExpressions:
                - key: planning.aurora.tce.io/servergroup
                  operator: In
                  values:
                  - cbs-snapshot-manager
                  - cbs-snapshot-capserver
          podAntiAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              - labelSelector:
                  matchExpressions:
                    - key: infra.tce.io/oam-app
                      operator: In
                      values:
                        - product-cbs-snap
                    - key: infra.tce.io/oam-comp
                      operator: In
                      values:
                        - cbs-snapshot-capserver
                topologyKey: planning.aurora.tce.io/hostname
      type: NonContainerJob
  images:
    product-cbs-snap:
      url: registry.tce.com/product-cbs-snap/product-cbs-snap:1.0.0-20230718-103844-01796eb.rhel.amd64
  namespace: tce
  registries:
    images: registry.tce.com
    packages: http://registry.tce.com:1181
  services: {}

deployment填写样例

kubernetes.io/hostname

tad:
  components:
    ocloud-tcenter-license:
      pdb: 
        minAvailable: 2
      replicas: 2
      labels:
        infra.tce.io/oam-app: ocloud-tcenter-license
        infra.tce.io/oam-comp: ocloud-tcenter-license        
      resources:
        ocloud-tcenter-license:
          limits:
            cpu: 1000m
            memory: 2048Mi
          requests:
            cpu: 100m
            memory: 500Mi
      scheduling:
        affinity:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
              - matchExpressions:
                - key: node-role.kubernetes.io/master
                  operator: Exists
          podAntiAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              - labelSelector:
                  matchExpressions:
                    - key: infra.tce.io/oam-app
                      operator: In
                      values:
                        - ocloud-tcenter-license
                    - key: infra.tce.io/oam-comp
                      operator: In
                      values:
                        - ocloud-tcenter-license
                topologyKey: kubernetes.io/hostname
        tolerations:
        - effect: NoSchedule
          key: node-role.kubernetes.io/master
          operator: Exists
      type: deployment

config填写样例

config执行的结果是生成一个同名的configMap，config中引用的配置大多是来自于全局配置，因此应用values.yaml中的配置项非常简洁。

tad:
  images:
    cam-redis:
      url: registry.tce.com/redis-tad-config/cam-redis:2.3.4-20230725-220428-4a1d24d.rhel.amd64
    yunapi-redis:
      url: registry.tce.com/redis-tad-config/yunapi-redis:2.3.4-20230725-220428-4a1d24d.rhel.amd64
  namespace: tce
  registries:
    images: registry.tce.com
    packages: http://registry.tce.com:1181

preset填写样例

tad:
  namespace: tce
  components:
    preset-tcs-yunapi-api3ocloud-tops:
      type: preset
      url: registry.tce.com/preset-tcs-yunapi-api3ocloud-tops:2.3.4-20230725-220428-4a1d24d.rhel.amd64

yunapi填写样例

tad:
  namespace: tce
  images:
    type: yunapi
    preset-ssm-yunapi-api3ocloud-ssm:
      url: registry.tce.com/preset-ssm-yunapi-api3ocloud-ssm/preset-ssm-yunapi-api3ocloud-ssm:1.0.0-20230702-201125-4e8b1eb.rhel.amd64
  registries:
    images: registry.tce.com
    packages: http://registry.tce.com:1181

dbsql填写样例

tad:
  images:
    dbsql-tsf-stack:
      url: registry.tce.com/dbsql-tsf-stack/dbsql-tsf-stack:1.0.0-20230413-113700-e6523e0.rhel.amd64
  namespace: tce
  registries:
    images: registry.tce.com
    packages: http://registry.tce.com:1181

应用私有化配置填写样例

tad提供的标准values.yaml下的字段基本上可以全部涵盖应用的部署配置需求，但不排除个别应用存在特殊需求，标准的tad字段无法满足，此时应用可以在tad同级字段定义私有配置。

tad
  images:
  namespaces: tce
--以上是tad字段的内容
--下面是私有配置内容,都写到selfConfigs字段下面
selfConfigs:
  networkCardBond: true
  deployMode: auto
  backupPolicy: nerver
  clusterType: multiple