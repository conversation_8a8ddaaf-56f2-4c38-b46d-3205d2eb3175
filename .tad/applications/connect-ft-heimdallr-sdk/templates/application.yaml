{{- $app := "connect-ft-heimdallr-sdk" -}}
{{- $component := index .Values.tad.components $app -}}

apiVersion: infra.tce.io/v1
kind: Application
metadata:
  labels:
    infra.tce.io/app-version: 1.0.0
  name: {{ $app }}
spec:
  components:
  - componentName: {{ $app }}-db
    dataOutputs:
    - name: db-ready
      conditions:
      - op: eq
        value: "Ready"
        fieldPath: "status.phase"
  - componentName: {{ $app }}-rabbitmq
    dataOutputs:
    - name: rabbitmq-ready
      conditions:
      - op: eq
        value: "Ready"
        fieldPath: "status.phase"
  - componentName: {{ $app }}-server
  ###service
  - componentName: {{ $app }}
    dataInputs:
    - valueFrom:
        dataOutputName: db-ready
    - valueFrom:
        dataOutputName: rabbitmq-ready
    traits:
    - trait:
        apiVersion: core.oam.dev/v1alpha2
        kind: ManualScalerTrait
        spec:
          replicaCount: {{ $component.replicas }}

---
apiVersion: infra.tce.io/v1
kind: Component
metadata:
  name: {{ $app }}
spec:
  workload:
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: {{$app}}
      labels:
        app: {{$app}}
    spec:
      replicas: {{ $component.replicas }}
      strategy:
        type: RollingUpdate
        rollingUpdate:
          maxSurge: 1
          maxUnavailable: 0
      minReadySeconds: 20
      revisionHistoryLimit: 3
      selector:
        matchLabels:
          app: {{$app}}
      template:
        metadata:
          labels:
            app: {{$app}}
        spec:
          terminationGracePeriodSeconds: 20
          restartPolicy: "Always"
          imagePullSecrets:
            - name: myregistrykey
          containers:
            - image: {{ (index .Values.tad.images $app).url }}
              name: {{$app}}
              imagePullPolicy: Always
              env:
                - name: DB_HOST
                  valueFrom:
                    configMapKeyRef:
                      name: {{ $app }}-db
                      key: host
                - name: DB_PORT
                  valueFrom:
                    configMapKeyRef:
                      name: {{ $app }}-db
                      key: port
                - name: DB_USER
                  valueFrom:
                    configMapKeyRef:
                      name: {{ $app }}-db
                      key: username
                - name: DB_PASSWORD
                  valueFrom:
                    configMapKeyRef:
                      name: {{ $app }}-db
                      key: password
                - name: DB_NAME
                  valueFrom:
                    configMapKeyRef:
                      name: {{ $app }}-db
                      key: dbname
                - name: RABBITMQ_HOST
                  valueFrom:
                    configMapKeyRef:
                      name: {{ $app }}-rabbitmq
                      key: host
                - name: RABBITMQ_PORT
                  valueFrom:
                    configMapKeyRef:
                      name: {{ $app }}-rabbitmq
                      key: port
                - name: RABBITMQ_USER
                  valueFrom:
                    configMapKeyRef:
                      name: {{ $app }}-rabbitmq
                      key: username
                - name: RABBITMQ_PASSWORD
                  valueFrom:
                    configMapKeyRef:
                      name: {{ $app }}-rabbitmq
                      key: password
                - name: RABBITMQ_VHOST
                  valueFrom:
                    configMapKeyRef:
                      name: {{ $app }}-rabbitmq
                      key: vhost
                - name: SERVER_PORT
                  value: "{{ .Values.config.serverPort }}"
                - name: CONSUMER_PORT
                  value: "{{ .Values.config.consumerPort }}"
                - name: PRODUCER_PORT
                  value: "{{ .Values.config.producerPort }}"
              ports:
                - containerPort: {{ .Values.config.managerPort }}
                  name: manager
                - containerPort: {{ .Values.config.serverPort }}
                  name: server
                - containerPort: {{ .Values.config.consumerPort }}
                  name: consumer
                - containerPort: {{ .Values.config.producerPort }}
                  name: producer
              resources:
                limits:
                  cpu: {{ $component.resources.connect-ft-heimdallr-sdk.limits.cpu }}
                  memory: {{ $component.resources.connect-ft-heimdallr-sdk.limits.memory }}
                requests:
                  cpu: {{ $component.resources.connect-ft-heimdallr-sdk.requests.cpu }}
                  memory: {{ $component.resources.connect-ft-heimdallr-sdk.requests.memory }}
              livenessProbe:
                httpGet:
                  path: /
                  port: {{ .Values.config.managerPort }}
                initialDelaySeconds: 30
                periodSeconds: 10
              readinessProbe:
                httpGet:
                  path: /
                  port: {{ .Values.config.managerPort }}
                initialDelaySeconds: 5
                periodSeconds: 10

---
apiVersion: infra.tce.io/v1
kind: Component
metadata:
  name: {{ $app }}-db
spec:
  workload:
    apiVersion: infra.tce.io/v1
    kind: Config
    metadata:
      name: {{ $app }}-db
      labels:
        app: {{ $app }}
      annotations:
        infra.tce.io/template-delimiter: "$$,$$"
    spec:
      dependencies:
        - dependencyName: db_app
          serviceID: aurora-mariadb-connect-ft-heimdallr-sdk-binding
      template:
          data:
            host: "$$ .Service.db_app.host $$"
            port: "$$ .Service.db_app.port $$"
            username: "$$ .Service.db_app.user $$"
            password: "$$ .Service.db_app.pass $$"
            dbname: "heimdallr_sdk"

---
apiVersion: infra.tce.io/v1
kind: Component
metadata:
  name: {{ $app }}-rabbitmq
spec:
  workload:
    apiVersion: infra.tce.io/v1
    kind: Config
    metadata:
      name: {{ $app }}-rabbitmq
      labels:
        app: {{ $app }}
      annotations:
        infra.tce.io/template-delimiter: "$$,$$"
    spec:
      dependencies:
        - dependencyName: db_rabbitmq
          serviceID: rabbitmq-connect-ft-heimdallr-sdk-binding
      template:
          data:
            host: "$$ .Service.db_rabbitmq.host $$"
            port: "$$ .Service.db_rabbitmq.port $$"
            username: "$$ .Service.db_rabbitmq.user $$"
            password: "$$ .Service.db_rabbitmq.pass $$"
            vhost: "/"

---
apiVersion: infra.tce.io/v1
kind: Component
metadata:
  name: {{ $app }}-server
spec:
  workload:
    apiVersion: v1
    kind: ConfigMap
    metadata:
      name: {{ $app }}-server
      labels:
        app: {{ $app }}
    data:
      server.js: |
            const server = {
              port: {{.Values.configmap.serverJS.port}},
              domain: '{{.Values.configmap.serverJS.domain}}{{.Values.tad.global.domainMain}}',
              apiDomain: '{{.Values.configmap.serverJS.apiDomain}}{{.Values.tad.global.domainMain}}',
              capiDomain: '{{.Values.configmap.serverJS.capiDomain}}{{.Values.tad.global.domainMain}}'
            };

            module.exports = server;

---
apiVersion: infra.tce.io/v1
kind: Component
metadata:
  name: {{ $app }}-service
spec:
  workload:
    apiVersion: v1
    kind: Service
    metadata:
      name: {{ $app }}
      labels:
        app: {{ $app }}
    spec:
      selector:
        app: {{ $app }}
      ports:
        - name: manager
          port: {{ .Values.config.managerPort }}
          targetPort: {{ .Values.config.managerPort }}
        - name: server
          port: {{ .Values.config.serverPort }}
          targetPort: {{ .Values.config.serverPort }}
        - name: consumer
          port: {{ .Values.config.consumerPort }}
          targetPort: {{ .Values.config.consumerPort }}
        - name: producer
          port: {{ .Values.config.producerPort }}
          targetPort: {{ .Values.config.producerPort }}
      type: ClusterIP

---
apiVersion: infra.tce.io/v1
kind: Component
metadata:
  name: {{ $app }}-ingress
spec:
  workload:
    apiVersion: traefik.containo.us/v1alpha1
    kind: IngressRoute
    metadata:
      name: {{ $app }}
    spec:
      entryPoints:
        - web
      routes:
        - kind: Rule
          match: Host(`{{.Values.configmap.serverJS.domain}}{{.Values.tad.global.domainMain}}`) && PathPrefix(`/api`)
          services:
            - name: {{ $app }}
              port: {{ .Values.config.serverPort }}
        - kind: Rule
          match: Host(`{{.Values.configmap.serverJS.domain}}{{.Values.tad.global.domainMain}}`)
          services:
            - name: {{ $app }}
              port: {{ .Values.config.managerPort }}
