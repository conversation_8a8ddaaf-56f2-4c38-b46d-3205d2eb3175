tad:
  global:
    domainMain: dev.heimdallr.woa.com
  namespace: jiguang
  registries:
    images: registry.tce.com
    packages: http://registry.tce.com:1181
  images:
    connect-ft-heimdallr-sdk:
      path: heimdallr
      url: place-holder
  instanceStrategy:
    level: global
  labels: {}

  components:
    connect-ft-heimdallr-sdk:
      type: Deployment
      replicas: 1
      images:
        connect-ft-heimdallr-sdk:
          ref: connect-ft-heimdallr-sdk
      probe:
        livenessInitialDelaySecond: 30
        readinessInitialDelaySecond: 5
      resources:
        connect-ft-heimdallr-sdk:
          requests:
            cpu: 1
            memory: 2Gi
          limits:
            cpu: 2
            memory: 4Gi
      labels:
        infra.tce.io/oam-app: connect-ft-heimdallr-sdk
        infra.tce.io/oam-comp: connect-ft-heimdallr-sdk
      scheduling:
        affinity:
          podAntiAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              - labelSelector:
                  matchExpressions:
                    - key: infra.tce.io/oam-app
                      operator: In
                      values:
                        - connect-ft-heimdallr-sdk
                    - key: infra.tce.io/oam-comp
                      operator: In
                      values:
                        - connect-ft-heimdallr-sdk
                topologyKey: kubernetes.io/hostname
      pdb:
        minAvailable: 1
    connect-ft-heimdallr-sdk-app:
      type: Deployment
      replicas: 1
      images:
        connect-ft-heimdallr-sdk:
          ref: connect-ft-heimdallr-sdk
      probe:
        livenessInitialDelaySecond: 30
        readinessInitialDelaySecond: 5
      resources:
        connect-ft-heimdallr-sdk-app:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 1
            memory: 2Gi
      labels:
        infra.tce.io/oam-app: connect-ft-heimdallr-sdk
        infra.tce.io/oam-comp: connect-ft-heimdallr-sdk-app
      scheduling:
        affinity:
          podAntiAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              - labelSelector:
                  matchExpressions:
                    - key: infra.tce.io/oam-app
                      operator: In
                      values:
                        - connect-ft-heimdallr-sdk
                    - key: infra.tce.io/oam-comp
                      operator: In
                      values:
                        - connect-ft-heimdallr-sdk-app
                topologyKey: kubernetes.io/hostname
      pdb:
        minAvailable: 1
    connect-ft-heimdallr-sdk-service:
      type: Deployment
      replicas: 1
      images:
        connect-ft-heimdallr-sdk:
          ref: connect-ft-heimdallr-sdk
      resources:
        connect-ft-heimdallr-sdk-service:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
      labels:
        infra.tce.io/oam-app: connect-ft-heimdallr-sdk
        infra.tce.io/oam-comp: connect-ft-heimdallr-sdk-service
      pdb:
        minAvailable: 1
config:
  managerPort: 8000
  serverPort: 8001
  consumerPort: 8002
  producerPort: 8003
###配置文件信息
configmap:
  dbJS:
    uname: '$$ .Service.db_app.user $$'
    upwd: '$$ .Service.db_app.pass $$'
    host: '$$ .Service.db_app.host $$'
    port: '$$ .Service.db_app.port $$'
    dbname: 'heimdallr_sdk'
  rabbitMQJS:
    enable: true
    host: '$$ .Service.db_rabbitmq.host $$'
    port: '$$ .Service.db_rabbitmq.port $$'
    username: '$$ .Service.db_rabbitmq.user $$'
    password: '$$ .Service.db_rabbitmq.pass $$'
    vhost: '/'
  serverJS:
    port: 8001
    domain: 'heimdallr.dev.'
    apiDomain: 'heimdallr.dev.'
    capiDomain: 'heimdallr.dev.'
