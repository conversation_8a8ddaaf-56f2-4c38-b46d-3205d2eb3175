server {
    listen 8000;
    server_name localhost;

    root /app/heimdallr-sdk/manager;
    index index.html;

    # 解决前端路由刷新404问题
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 代理到server服务
    location /api/server/ {
        proxy_pass http://localhost:8001/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 代理到consumer服务
    location /api/consumer/ {
        proxy_pass http://localhost:8002/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 代理到producer服务
    location /api/producer/ {
        proxy_pass http://localhost:8003/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 默认API代理到server服务
    location /api/ {
        proxy_pass http://localhost:8001/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 直接访问后端服务的静态资源
    location /browser-dist/ {
        proxy_pass http://localhost:8001/browser-dist/;
    }

    location /customer-dist/ {
        proxy_pass http://localhost:8001/customer-dist/;
    }

    location /dom-dist/ {
        proxy_pass http://localhost:8001/dom-dist/;
    }

    location /fetch-dist/ {
        proxy_pass http://localhost:8001/fetch-dist/;
    }

    location /hash-dist/ {
        proxy_pass http://localhost:8001/hash-dist/;
    }

    location /history-dist/ {
        proxy_pass http://localhost:8001/history-dist/;
    }

    location /crash-dist/ {
        proxy_pass http://localhost:8001/crash-dist/;
    }

    location /crash-worker/ {
        proxy_pass http://localhost:8001/crash-worker/;
    }

    location /performance-dist/ {
        proxy_pass http://localhost:8001/performance-dist/;
    }

    location /xhr-dist/ {
        proxy_pass http://localhost:8001/xhr-dist/;
    }

    location /record-dist/ {
        proxy_pass http://localhost:8001/record-dist/;
    }

    # 增加超时设置
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;

    error_log /var/log/nginx/heimdallr_error.log;
    access_log /var/log/nginx/heimdallr_access.log;
}
