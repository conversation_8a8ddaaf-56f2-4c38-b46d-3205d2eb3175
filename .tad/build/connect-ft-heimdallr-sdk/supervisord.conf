[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info
pidfile=/var/run/supervisord.pid

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
startretries=5
stderr_logfile=/var/log/supervisor/nginx.err.log
stdout_logfile=/var/log/supervisor/nginx.out.log

[program:server]
command=node /app/heimdallr-sdk/server/dist/index.js
directory=/app/heimdallr-sdk/server
autostart=true
autorestart=true
startretries=3
startsecs=5
stopwaitsecs=10
stderr_logfile=/var/log/supervisor/server.err.log
stdout_logfile=/var/log/supervisor/server.out.log
environment=NODE_ENV=production,SERVER_PORT=8001,DB_HOST=%(ENV_DB_HOST)s,DB_PORT=%(ENV_DB_PORT)s,DB_USER=%(ENV_DB_USER)s,DB_PASSWORD=%(ENV_DB_PASSWORD)s,DB_NAME=%(ENV_DB_NAME)s

[program:consumer]
command=node /app/heimdallr-sdk/consumer/dist/index.js
directory=/app/heimdallr-sdk/consumer
autostart=true
autorestart=true
startretries=3
startsecs=5
stopwaitsecs=10
stderr_logfile=/var/log/supervisor/consumer.err.log
stdout_logfile=/var/log/supervisor/consumer.out.log
environment=NODE_ENV=production,SERVER_PORT=8002,DB_HOST=%(ENV_DB_HOST)s,DB_PORT=%(ENV_DB_PORT)s,DB_USER=%(ENV_DB_USER)s,DB_PASSWORD=%(ENV_DB_PASSWORD)s,DB_NAME=%(ENV_DB_NAME)s,RABBITMQ_HOST=%(ENV_RABBITMQ_HOST)s,RABBITMQ_PORT=%(ENV_RABBITMQ_PORT)s,RABBITMQ_USER=%(ENV_RABBITMQ_USER)s,RABBITMQ_PASSWORD=%(ENV_RABBITMQ_PASSWORD)s,RABBITMQ_VHOST=%(ENV_RABBITMQ_VHOST)s

[program:producer]
command=node /app/heimdallr-sdk/producer/dist/index.js
directory=/app/heimdallr-sdk/producer
autostart=true
autorestart=true
startretries=3
startsecs=5
stopwaitsecs=10
stderr_logfile=/var/log/supervisor/producer.err.log
stdout_logfile=/var/log/supervisor/producer.out.log
environment=NODE_ENV=production,SERVER_PORT=8003,RABBITMQ_HOST=%(ENV_RABBITMQ_HOST)s,RABBITMQ_PORT=%(ENV_RABBITMQ_PORT)s,RABBITMQ_USER=%(ENV_RABBITMQ_USER)s,RABBITMQ_PASSWORD=%(ENV_RABBITMQ_PASSWORD)s,RABBITMQ_VHOST=%(ENV_RABBITMQ_VHOST)s
