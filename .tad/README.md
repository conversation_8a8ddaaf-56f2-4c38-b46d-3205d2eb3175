# connect-ft-heimdallr-sdk 容器化部署指南

本文档提供了如何使用 `.tad` 配置文件来容器化部署 connect-ft-heimdallr-sdk 项目的详细说明。

## 项目结构

connect-ft-heimdallr-sdk 项目包含前端和后端两个主要部分：

- **前端部分**：监控 SDK 客户端库（browser, node, wx 等）和监控管理页面（manager）
- **后端部分**：
  - 服务器组件（playground/server）：接收和处理监控数据，端口8001
  - 消费者服务（playground/server_consumer）：处理消息队列中的数据，端口8002
  - 生产者服务（playground/server_producer）：接收前端上报的数据并推送到消息队列，端口8003

## 容器化架构

connect-ft-heimdallr-sdk 的容器化部署使用了以下技术和架构：

1. **多阶段构建**：使用Docker多阶段构建减小最终镜像大小
2. **Supervisor进程管理**：在单个容器中管理多个Node.js服务
3. **环境变量配置**：通过环境变量注入数据库和消息队列配置
4. **Kubernetes部署**：使用Helm Chart进行Kubernetes部署

## 前置条件

- Docker
- Node.js v20+
- pnpm 10.11.0+
- Kubernetes 集群（仅用于集群部署）
- kubectl 命令行工具（仅用于集群部署）
- Helm（可选，但推荐用于集群部署）

## CI/CD部署流程

connect-ft-heimdallr-sdk 项目使用标准的CI/CD流程进行部署，流程如下：

1. **代码提交**：开发人员将代码提交到代码仓库
2. **CI/CD触发**：代码提交触发CI/CD流程
3. **构建Docker镜像**：
   - 根据目标环境和架构选择合适的Dockerfile
   - 构建Docker镜像
   - 将镜像推送到镜像仓库
4. **部署到Kubernetes**：
   - 使用Helm Chart部署应用到Kubernetes集群
   - 更新values.yaml中的镜像地址
   - 执行`helm upgrade`或`helm install`命令

### Kubernetes集群部署

如果需要部署到Kubernetes集群，请按照以下步骤操作：

```bash
# 构建并标记镜像
docker build -t your-registry/connect-ft-heimdallr-sdk:latest -f .tad/build/connect-ft-heimdallr-sdk/Dockerfile.connect-ft-heimdallr-sdk.amd64 .
docker push your-registry/connect-ft-heimdallr-sdk:latest

# 使用Helm部署
helm upgrade --install connect-ft-heimdallr-sdk .tad/applications/connect-ft-heimdallr-sdk \
  --namespace heimdallr \
  --set tad.images.connect-ft-heimdallr-sdk.url=your-registry/connect-ft-heimdallr-sdk:latest
```

## 目录结构说明

`.tad` 目录包含了所有容器化部署相关的配置文件：

```
.tad/
├── README.md                                           # 本文档
├── applications/                                       # Kubernetes应用配置
│   └── connect-ft-heimdallr-sdk/                       # Helm Chart
│       ├── templates/                                  # Kubernetes模板
│       │   └── application.yaml                        # 应用定义
│       └── values.yaml                                 # 配置值
└── build/                                              # 构建相关文件
    └── connect-ft-heimdallr-sdk/                       # 构建配置
        ├── Dockerfile.connect-ft-heimdallr-sdk.amd64   # AMD64架构Dockerfile
        ├── Dockerfile.connect-ft-heimdallr-sdk.arm64   # ARM64架构Dockerfile
        ├── nginx.conf                                  # Nginx配置
        └── supervisord.conf                            # Supervisor配置
```

## 访问应用

部署完成后，您可以通过以下方式访问应用：

- 前端页面：`http://heimdallr.dev.heimdallr.woa.com/`
- 后端API：`http://heimdallr.dev.heimdallr.woa.com/api/`

## 故障排除

如果遇到问题，请尝试以下方法：

1. **检查Pod状态**：
   ```bash
   kubectl get pods -n heimdallr
   ```

2. **查看Pod日志**：
   ```bash
   kubectl logs <pod-name> -n heimdallr
   ```

3. **检查服务状态**：
   ```bash
   kubectl exec -it <pod-name> -n heimdallr -- supervisorctl status
   ```

4. **重启服务**：
   ```bash
   kubectl exec -it <pod-name> -n heimdallr -- supervisorctl restart <service-name>
   ```
