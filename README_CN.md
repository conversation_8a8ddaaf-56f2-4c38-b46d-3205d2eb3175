# HEIMDALLR-SDK

一款简单易用、轻量化、插件化的前端监控SDK

## 项目概述

Heimdallr-SDK是一个全面的前端监控解决方案，包含以下组件：

- **前端监控SDK**：可集成到任何Web应用中的轻量级监控工具
- **监控管理平台**：用于查看和分析监控数据的Web界面
- **后端服务**：处理和存储监控数据的服务器组件

本项目采用微服务架构，包含三个主要服务：

1. **Server**：主要API服务，处理前端页面请求和数据查询
2. **Consumer**：消息队列消费者，处理异步任务和数据分析
3. **Producer**：消息队列生产者，接收并转发监控数据

## 环境要求

- **Node.js**: `v20.16.0`或更高版本
- **包管理器**: `pnpm`
- **数据库**: MySQL 5.7+
- **消息队列**: RabbitMQ 3.8+
- **容器化**: Docker 20.10+

## 本地开发

### 安装依赖

```bash
pnpm i --registry=https://registry.npmmirror.com
```

### 数据库配置

确保本地MySQL数据库服务已启动：

- 主机: localhost
- 端口: 3306
- 用户名: root
- 密码: 根据本地设置
- 数据库名: heimdallr_sdk

### 初始化数据库

```bash
pnpm run prisma
```

### 启动本地服务

```bash
pnpm run dev
```

启动后可通过以下地址访问：

- 前端监控页面: http://localhost:8000
- 服务器API: http://localhost:8001
- 消费者API: http://localhost:8002
- 生产者API: http://localhost:8003

## 构建

### 构建单个包

```bash
pnpm --filter [packageName] run build
```

例如，构建浏览器包：

```bash
pnpm --filter @heimdallr-sdk/browser run build
```

### 构建所有包

```bash
pnpm run build
```

## 容器化部署

本项目支持Docker容器化部署，将前端监控页面和后端服务打包到一个容器中。

### 前提条件

- 安装Docker
- 确保MySQL数据库可访问

### 部署步骤

1. **克隆仓库**

```bash
git clone https://github.com/LuciferHuang/heimdallr-sdk.git
cd heimdallr-sdk
```

2. **配置环境变量**

编辑`deploy-heimdallr.sh`脚本，设置数据库连接信息：

```bash
# 数据库配置
DB_HOST="your-db-host"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="your-password"
DB_NAME="heimdallr_sdk"
```

3. **执行部署脚本**

```bash
./deploy-heimdallr.sh
```

脚本将自动执行以下操作：
- 构建Docker镜像
- 启动容器
- 配置服务

4. **访问服务**

部署完成后，可通过以下地址访问服务：

- 前端监控页面: http://localhost:8000
- 服务器API: http://localhost:8001
- 消费者API: http://localhost:8002
- 生产者API: http://localhost:8003

### 容器管理

```bash
# 停止容器
docker stop heimdallr-sdk

# 启动容器
docker start heimdallr-sdk

# 重启容器
docker restart heimdallr-sdk

# 查看容器日志
docker logs heimdallr-sdk

# 进入容器
docker exec -it heimdallr-sdk bash
```

### 前端构建与更新

如需更新前端页面，可在本地构建后复制到容器：

```bash
# 在本地构建前端
cd playground/manager
pnpm install
pnpm run build:private

# 将构建结果复制到容器
docker cp dist/. heimdallr-sdk:/app/dist/manager/

# 重启容器
docker restart heimdallr-sdk
```

## API文档

### 服务器API (端口8001)

- `GET /project/init`: 初始化项目
- `GET /project/list`: 获取项目列表
- `GET /statistic/total`: 获取总体统计数据
- `GET /statistic/proj`: 获取项目统计数据
- `POST /log/report`: 上报日志
- `GET /log/list`: 获取日志列表
- `GET /log/detail`: 获取日志详情
- `POST /sourcemap/upload`: 上传sourcemap
- `GET /sourcemap/search`: 搜索sourcemap
- `GET /session/list`: 获取会话列表
- `GET /session/detail`: 获取会话详情

### 消费者API (端口8002)

- `GET /project/list`: 获取项目列表
- `GET /log/list`: 获取日志列表
- `GET /log/detail`: 获取日志详情

### 生产者API (端口8003)

- `GET /project/init`: 初始化项目
- `POST /log/report`: 上报日志
- `POST /sourcemap/upload`: 上传sourcemap

## 项目结构

```
heimdallr-sdk/
├── .tad/                  # 容器化部署配置
├── clients/               # 客户端SDK
├── packages/              # 核心包
├── playground/            # 开发环境
│   ├── manager/           # 前端监控页面
│   ├── server/            # 主服务器
│   ├── server_consumer/   # 消费者服务
│   └── server_producer/   # 生产者服务
├── tools/                 # 工具脚本
├── deploy-heimdallr.sh    # 部署脚本
└── README.md              # 项目说明
```

## 文档

详细文档请访问 [heimdallr-sdk](https://luciferhuang.github.io/heimdallr-sdk/)。

## 贡献

欢迎提交问题和贡献代码，请遵循项目的贡献指南。

## 许可证

MIT

## 持续更新中...

本项目正在持续优化和更新，敬请关注最新进展。
