执行步骤
后端转发API
1. kubectl create namespace tcs-store


2. stripPrefix:

cat <<EOF | kubectl apply -f -
apiVersion: traefik.containo.us/v1alpha1
kind: Middleware
metadata:
  name: apigw-mid-tcsstore-next
  namespace: tcs-store
spec:
  stripPrefix:
    prefixes:
    - /cgw
EOF

3. create service

cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Service
metadata:
  name: tcs-apigw
  namespace: tcs-store
spec:
  ports:
  - port: 8888
    protocol: TCP
    targetPort: 8888
  sessionAffinity: None
  type: ClusterIP
EOF

4. create endpoints

cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Endpoints
metadata:
  name: tcs-apigw
  namespace: tcs-store
subsets:
- addresses:
  - ip: *************
  ports:
  - port: 8082
    protocol: TCP
EOF

5. create ingressroute

cat <<EOF | kubectl apply -f -
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: tcs-store
  namespace: tcs-store
spec:
  entryPoints:
  - web
  routes:
  - kind: Rule
    match: (Host(\`tcs-store.woa.com\`)) && PathPrefix(\`/cgw\`)
    middlewares:
    - name: remove-headers
      namespace: jiguang-prod
    - name: cors-header
      namespace: jiguang-prod
    - name: apigw-mid-tcsstore-next
      namespace: tcs-store
    services:
    - name: tcs-apigw
      namespace: tcs-store
      port: 8888
EOF

________________NEXT
6. create nodeport

cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Service
metadata:
  labels:
    app: apigw
  name: apigw-nodeport
  namespace: tcs-store
spec:
  externalTrafficPolicy: Cluster
  ports:
  - name: http
    nodePort: 31083  # 选择一个不同的端口，避免与现有服务冲突
    port: 8888
    protocol: TCP
    targetPort: 8888
  selector:
    app: apigw
  sessionAffinity: None
  type: NodePort
EOF
前端转发资源请求
1. 首先创建Service (参考tcs-imgcache-pre的配置):

bashcat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Service
metadata:
  name: tcs-imgcache
  namespace: tcs-store
spec:
  ports:
  - port: 30600
    protocol: TCP
    targetPort: 30600
  sessionAffinity: None
  type: ClusterIP
EOF


2. 创建Endpoints:

cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Endpoints
metadata:
  name: tcs-imgcache
  namespace: tcs-store
subsets:
- addresses:
  - ip: *************  # 与jiguang-pre使用的相同IP
  - ip: ************   # 与jiguang-pre使用的相同IP
  ports:
  - port: 30600        # 我们选择的新端口
    protocol: TCP
EOF


3.更新ingressroute:

cat <<EOF | kubectl apply -f -
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: tcs-store
  namespace: tcs-store
spec:
  entryPoints:
  - web
  routes:
  - kind: Rule
    match: (Host(\`tcs-store.woa.com\`)) && PathPrefix(\`/cgw\`)
    middlewares:
    - name: remove-headers
      namespace: jiguang-prod
    - name: cors-header
      namespace: jiguang-prod
    - name: apigw-mid-tcsstore-next
      namespace: tcs-store
    services:
    - name: tcs-apigw
      namespace: tcs-store
      port: 8888
  - kind: Rule
    match: Host(\`tcs-store.woa.com\`)
    middlewares:
    - name: remove-headers
      namespace: jiguang-prod
    - name: cors-header
      namespace: jiguang-prod
    - name: apigw-mid-tcsstore-next
      namespace: tcs-store
    services:
    - name: tcs-imgcache
      namespace: tcs-store
      port: 30600
EOF

____________________________NEXT集群
4. 首先创建ConfigMap（配置了nginx）：
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: tcs-imgcache-nginx-config
  namespace: tcs-store
data:
  nginx.conf: |
    user nginx;
    worker_processes  2;
    events {
      # TODO set ulimit
      worker_connections  10240;
    }
    http {
      include /etc/nginx/mime.types;
      default_type application/octet-stream;
      server_tokens off;

      sendfile on;
      tcp_nopush on;
      tcp_nodelay on;

      log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
      '$status $body_bytes_sent "$http_referer" '
      '"$http_user_agent" "$http_x_forwarded_for" '
      '"$request_time" "$upstream_connect_time" ';

      access_log  /dev/stdout  main;
      error_log  /dev/stdout  debug;

      gzip on;
      gzip_proxied any;
      gzip_vary on;
      gzip_types "*";

      # Settings for S3 cache
      proxy_cache_path /var/cache/nginx/s3_proxy
        levels=1:2
        keys_zone=s3_cache:10m
        max_size=10g
        inactive=60m
        use_temp_path=off;

      map \$uri \$custom_content_type {
          default "text/html";
          ~(.*\.json)$ "application/json";
          ~(.*\.js)$ "application/x-javascript";
          ~(.*\.gif)$ "image/gif";
          ~(.*\.jpeg)$ "image/jpeg";
          ~(.*\.jpg)$ "image/jpeg";
          ~(.*\.css)$ "text/css";
          ~(.*\.xml)$ "text/xml";
          ~(.*\.rss)$ "application/rss+xml";
          ~(.*\.svg)$ "image/svg+xml";
          ~(.*\.png)$ "image/png";
      }
      server {
        listen       80;
        server_name  imgcache.tcs-store.woa.com;

        set \$cors_origin "http://tcs-store.woa.com";
        if (\$http_origin ~* "^http?://.*.tcs-store.woa.com"){
            set \$cors_origin \$http_origin;
        }

        if (\$request_method = OPTIONS) {
            return 204;
        }
        # Proxy caching configuration
        proxy_cache s3_cache;
        proxy_cache_valid 200 302 10s;
        proxy_cache_valid 404 5s;
        proxy_cache_valid 403 10s;
        proxy_cache_methods GET HEAD;
        proxy_cache_convert_head off;
        proxy_cache_revalidate on;
        proxy_cache_background_update on;
        proxy_cache_lock on;
        proxy_cache_use_stale error timeout http_500 http_502 http_503 http_504;
        proxy_cache_key "\$request_method\$host\$uri";

        location / {
          add_header 'Access-Control-Allow-Origin' '\$cors_origin' always;
          add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
          add_header 'Access-Control-Allow-Headers' 'Accept,Authorization,Cache-Control,Content-Type,DNT,If-Modified-Since,Keep-Alive,Origin,User-Agent,X-Requested-With' always;
          add_header 'Access-Control-Allow-Credentials' 'true';
          add_header 'Cache-Control' 'no-cache';
          add_header 'Content-Type' \$custom_content_type;

          proxy_hide_header Content-Type;
          add_header Content-Type \$custom_content_type;

          # 处理特殊路径（最具体的规则放在前面）
          rewrite ^/imgcache/(.*) /tcs-store/\$1 break;
          # 处理默认路径（最通用的规则放在最后）
          rewrite ^/ /tcs-store/index.html break;
          proxy_pass http://127.0.0.1:1181;
        }
      }
      
      # 新增服务器块 - cloud.tencent.com域名
      server {
        listen       80;
        server_name  tcs-store.cloud.tencent.com;

        set \$cors_origin "http://tcs-store.cloud.tencent.com";
        if (\$http_origin ~* "^http?://.*.tcs-store.cloud.tencent.com"){
            set \$cors_origin \$http_origin;
        }

        if (\$request_method = OPTIONS) {
            return 204;
        }
        
        # Proxy caching configuration
        proxy_cache s3_cache;
        proxy_cache_valid 200 302 10s;
        proxy_cache_valid 404 5s;
        proxy_cache_valid 403 10s;
        proxy_cache_methods GET HEAD;
        proxy_cache_convert_head off;
        proxy_cache_revalidate on;
        proxy_cache_background_update on;
        proxy_cache_lock on;
        proxy_cache_use_stale error timeout http_500 http_502 http_503 http_504;
        proxy_cache_key "\$request_method\$host\$uri";

        location / {
          add_header 'Access-Control-Allow-Origin' '\$cors_origin' always;
          add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
          add_header 'Access-Control-Allow-Headers' 'Accept,Authorization,Cache-Control,Content-Type,DNT,If-Modified-Since,Keep-Alive,Origin,User-Agent,X-Requested-With' always;
          add_header 'Access-Control-Allow-Credentials' 'true';
          add_header 'Cache-Control' 'no-cache';
          add_header 'Content-Type' \$custom_content_type;

          proxy_hide_header Content-Type;
          add_header Content-Type \$custom_content_type;

          rewrite ^/imgcache/docs/tcs_platform/\d+$ /cloud/docs/tcs_platform/index.html break;
          # 处理特殊路径（最具体的规则放在前面）
          rewrite ^/imgcache/(.*) /cloud/\$1 break;
          # 处理默认路径（最通用的规则放在最后）
          rewrite ^/ /cloud/index.html break;
          proxy_pass http://127.0.0.1:1181;
        }
      }
    }
EOF


5. 创建Deployment（包含nginx和fileserver容器）：
cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/component: tcs-imgcache-fileserver
  name: tcs-imgcache-fileserver
  namespace: tcs-store
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/component: tcs-imgcache-fileserver
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/component: tcs-imgcache-fileserver
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app.kubernetes.io/component
                operator: In
                values:
                - tcs-imgcache-fileserver
            topologyKey: kubernetes.io/hostname
      containers:
      - image: registry.tce.com/library/tcs-imgcache-nginx:v1.21.5-amd64
        imagePullPolicy: IfNotPresent
        name: nginx
        resources:
          limits:
            cpu: "4"
            memory: 8Gi
          requests:
            cpu: 100m
            memory: 256Mi
        volumeMounts:
        - mountPath: /etc/nginx/nginx.conf
          name: imgcache-nginx-conf
          readOnly: true
          subPath: nginx.conf
      - args:
        - -storage-type=cos
        - -root-path=\$(S3_IMAGE_ROOT_DIR)
        - -cos-endpoint=\$(S3_HOST)
        - -cos-bucket=\$(S3_BUCKET)
        - -cos-secret-id=\$(S3_ACCESS_KEY)
        - -cos-secret-key=\$(S3_SECRET_KEY)
        env:
        - name: S3_ACCESS_KEY
          value: 去申请
        - name: S3_SECRET_KEY
          value: 去申请
        - name: S3_HOST
          value: cos.ap-nanjing.myqcloud.com
        - name: S3_PORT
          value: "80"
        - name: S3_BUCKET
          value: tcs-store-1258344699
        - name: S3_REGION
          value: ap-nanjing
        - name: S3_IMAGE_ROOT_DIR
          value: /
        image: registry.jiguang.woa.com/fileserver-imgcache/fileserver:1.0.0-20230822-171013-eae3294.rhel.amd64
        imagePullPolicy: IfNotPresent
        name: fileserver
        resources:
          limits:
            cpu: "4"
            memory: 8Gi
          requests:
            cpu: 100m
            memory: 256Mi
      volumes:
      - configMap:
          items:
          - key: nginx.conf
            path: nginx.conf
          name: tcs-imgcache-nginx-config
        name: imgcache-nginx-conf
EOF

6. 创建NodePort服务：
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Service
metadata:
  name: tcs-imgcache-fileserver
  namespace: tcs-store
  labels:
    app.kubernetes.io/component: tcs-imgcache-fileserver
spec:
  selector:
    app.kubernetes.io/component: tcs-imgcache-fileserver
  type: NodePort
  ports:
  - name: nginx-insecure
    port: 80
    targetPort: 80
    nodePort: 30600
  externalTrafficPolicy: Cluster
EOF
