# Heimdallr-SDK 快速入门指南

本指南将帮助您快速上手Heimdallr-SDK项目，包括部署、配置和基本使用。

## 1. 项目简介

Heimdallr-SDK是一个全面的前端监控解决方案，包含：

- **前端监控SDK**：集成到Web应用中收集性能和错误数据
- **监控管理平台**：可视化展示和分析监控数据
- **后端服务**：处理和存储监控数据

## 2. 快速部署

### 使用Docker部署（推荐）

1. **克隆仓库**
   ```bash
   git clone https://github.com/LuciferHuang/heimdallr-sdk.git
   cd heimdallr-sdk
   ```

2. **执行部署脚本**
   ```bash
   ./deploy-heimdallr.sh
   ```

3. **访问服务**
   - 前端监控页面: http://localhost:8000
   - 服务器API: http://localhost:8001

### 本地开发环境

1. **安装依赖**
   ```bash
   pnpm i --registry=https://registry.npmmirror.com
   ```

2. **启动服务**
   ```bash
   pnpm run dev
   ```

## 3. 使用监控SDK

### 在Web应用中集成SDK

1. **安装SDK**
   ```bash
   npm install @heimdallr-sdk/browser
   ```

2. **初始化SDK**
   ```javascript
   import { init } from '@heimdallr-sdk/browser';
   
   init({
     dsn: {
       host: 'your-server-host:8001',
       init: '/project/init',
       report: '/log/report'
     },
     app: {
       name: 'YourAppName',
       leader: 'YourName',
       desc: 'Your application description'
     },
     plugins: [
       // 选择需要的插件
     ]
   });
   ```

3. **可用插件**
   - 错误监控
   - 性能监控
   - 用户行为监控
   - 网络请求监控
   - 自定义事件监控

## 4. 使用监控管理平台

### 登录平台

访问 http://localhost:8000 登录监控管理平台。

### 主要功能

1. **项目管理**
   - 创建和管理监控项目
   - 配置项目参数

2. **错误监控**
   - 查看错误列表
   - 分析错误详情
   - 错误源码定位

3. **性能监控**
   - 页面加载性能
   - 资源加载性能
   - 用户体验指标

4. **用户行为分析**
   - 用户操作记录
   - 会话回放
   - 行为轨迹分析

5. **统计报表**
   - 错误趋势分析
   - 性能指标统计
   - 自定义报表

## 5. API接口说明

### 主要API端点

1. **项目相关**
   - `GET /project/init`: 初始化项目
   - `GET /project/list`: 获取项目列表

2. **日志相关**
   - `POST /log/report`: 上报日志
   - `GET /log/list`: 获取日志列表
   - `GET /log/detail`: 获取日志详情

3. **统计相关**
   - `GET /statistic/total`: 获取总体统计数据
   - `GET /statistic/proj`: 获取项目统计数据

4. **SourceMap相关**
   - `POST /sourcemap/upload`: 上传sourcemap
   - `GET /sourcemap/search`: 搜索sourcemap

## 6. 常见问题

### SDK集成问题

**问题**: SDK初始化后没有数据上报
**解决**: 
- 确认服务器地址配置正确
- 检查网络连接
- 查看浏览器控制台是否有错误

### 平台访问问题

**问题**: 无法访问监控平台
**解决**:
- 确认容器正常运行
- 检查端口映射
- 查看容器日志

### 数据查询问题

**问题**: 平台上看不到监控数据
**解决**:
- 确认SDK正确集成并上报数据
- 检查数据库连接
- 验证数据处理服务正常运行

## 7. 下一步

- 阅读[完整文档](https://luciferhuang.github.io/heimdallr-sdk/)
- 查看[部署指南](./DEPLOYMENT_GUIDE.md)
- 探索[API文档](https://luciferhuang.github.io/heimdallr-sdk/api)
- 了解[插件开发](https://luciferhuang.github.io/heimdallr-sdk/plugins)

## 8. 获取帮助

- 提交[Issue](https://github.com/LuciferHuang/heimdallr-sdk/issues)
- 查看[FAQ](https://luciferhuang.github.io/heimdallr-sdk/faq)
- 加入社区讨论
