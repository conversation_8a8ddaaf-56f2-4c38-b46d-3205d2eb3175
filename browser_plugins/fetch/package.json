{"name": "@heimdallr-sdk/fetch", "version": "0.0.20", "description": "A Plugin For Browser SDK", "license": "MIT", "author": "<PERSON><PERSON><PERSON>", "main": "dist/fetch.umd.js", "module": "esm/index.js", "types": "esm/index.d.ts", "files": ["dist", "esm"], "homepage": "https://github.com/LuciferHuang/heimdallr-sdk/blob/main/packages/fetch/README.md", "repository": {"type": "git", "url": "git+https://github.com/LuciferHuang/heimdallr-sdk.git"}, "bugs": {"url": "https://github.com/LuciferHuang/heimdallr-sdk/issues"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "sdk", "browser", "fetch", "monitor"], "publishConfig": {"access": "public"}, "scripts": {"dev": "rimraf esm && rollup -c rollup.config.dev.js -w", "build": "rimraf dist esm && rollup -c"}, "dependencies": {"@heimdallr-sdk/types": "workspace:^", "@heimdallr-sdk/utils": "workspace:^"}}