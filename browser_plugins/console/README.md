# @heimdallr-sdk/console

> Capture console output and report

## Usage

### cdn

```html
<script src="[console-dist]/console.iife.js"></script>
<script>
    window.__HEIMDALLR_OPTIONS__ = {
        dsn: {
            host: 'localhost:8888',
            init: '/project/init',
            upload: '/log/upload'
        },
        app: {
            name: 'playgroundAP<PERSON>',
            leader: 'test',
            desc: 'test proj'
        },
        plugins: [
            HEIMDALLR_CONSOLE(),
        ]
    };
</script>
<script async src="/browser-dist/browser.iife.js"></script>
```

### npm

```js
import heimdallr from "@heimdallr-sdk/browser";
import consolePlugin from "@heimdallr-sdk/console";
heimdallr({
    dsn: {
        host: 'localhost:8888',
        init: '/project/init',
        upload: '/log/upload'
    },
    app: {
        name: 'playground<PERSON><PERSON>',
        leader: 'test',
        desc: 'test proj'
    },
    plugins: [
        consolePlugin(),
    ]
});
```

## 上报数据

### 日志上报

|字段名称|描述|
|-|-|
|args|额外参数|
|level|console类型|
