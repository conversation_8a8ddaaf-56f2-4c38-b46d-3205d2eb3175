{"name": "@heimdallr-sdk/dom", "version": "0.0.21", "description": "A Plugin For Browser SDK", "license": "MIT", "author": "<PERSON><PERSON><PERSON>", "main": "dist/dom.umd.js", "module": "esm/index.js", "types": "esm/index.d.ts", "files": ["dist", "esm"], "homepage": "https://github.com/LuciferHuang/heimdallr-sdk/blob/main/packages/dom/README.md", "repository": {"type": "git", "url": "git+https://github.com/LuciferHuang/heimdallr-sdk.git"}, "bugs": {"url": "https://github.com/LuciferHuang/heimdallr-sdk/issues"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "sdk", "browser", "dom", "monitor"], "publishConfig": {"access": "public"}, "scripts": {"dev": "rimraf esm && rollup -c rollup.config.dev.js -w", "build": "rimraf dist esm && rollup -c"}, "dependencies": {"@heimdallr-sdk/types": "workspace:^", "@heimdallr-sdk/utils": "workspace:^"}}