---
# https://vitepress.dev/reference/default-theme-home-page
layout: home

hero:
  name: "HEIMDALLR-SDK"
  text: "前端监控sdk"
  tagline: 一款简单易用、轻量化、插件化的前端监控sdk
  actions:
    - theme: brand
      text: 开始使用
      link: /usage/browser

features:
  - title: 🐱‍👤 非侵入式
    details: 能够不侵入业务代码并及时上报系统状态（报错、使用情况等）
  - title: 🐱‍🏍 按需引入
    details: 除了基座是必须引入的，其余 sdk 的功能都将以插件的方式按需引入
  - title: 🐱‍💻 覆盖场景全
    details: 覆盖常见的前端场景，如：浏览器、小程序
  - title: 🐱‍🚀 友好易扩展、易使用
    details: 允许引入自定义开发的插件，扩展 sdk 的能力； 提供了 监控管理后台 与 监控服务，可以使用 cli 工具完成快速部署，支持二开
---
