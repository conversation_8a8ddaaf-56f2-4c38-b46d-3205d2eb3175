# Heimdallr-SDK 容器化部署指南

本文档详细说明了如何使用Docker容器化部署Heimdallr-SDK项目，包括前端监控页面和后端服务。

## 目录

- [前提条件](#前提条件)
- [部署步骤](#部署步骤)
- [配置说明](#配置说明)
- [常见问题](#常见问题)
- [高级配置](#高级配置)
- [维护指南](#维护指南)

## 前提条件

在开始部署之前，请确保满足以下条件：

1. **Docker环境**：
   - Docker 20.10+
   - Docker Compose (可选)

2. **数据库**：
   - MySQL 5.7+
   - 创建名为`heimdallr_sdk`的数据库
   - 具有读写权限的数据库用户

3. **消息队列**（可选）：
   - RabbitMQ 3.8+

4. **网络**：
   - 确保容器可以访问数据库
   - 开放必要的端口（8000-8003）

## 部署步骤

### 1. 克隆代码仓库

```bash
git clone https://github.com/LuciferHuang/heimdallr-sdk.git
cd heimdallr-sdk
```

### 2. 配置环境变量

编辑`deploy-heimdallr.sh`脚本，设置数据库连接信息：

```bash
# 数据库配置
DB_HOST="your-db-host"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="your-password"
DB_NAME="heimdallr_sdk"

# RabbitMQ配置（如果使用）
RABBITMQ_HOST="your-rabbitmq-host"
RABBITMQ_PORT="5672"
RABBITMQ_USER="guest"
RABBITMQ_PASSWORD="guest"
RABBITMQ_VHOST="/"
```

### 3. 执行部署脚本

有三种部署方式可供选择：

#### 方式一：架构特定构建（推荐用于生产环境）

```bash
chmod +x deploy-heimdallr.sh
./deploy-heimdallr.sh
```

脚本会自动检测系统架构（AMD64或ARM64），并使用对应的优化Dockerfile。

#### 方式二：多阶段构建（适用于CI/CD环境）

```bash
chmod +x deploy-heimdallr.sh
./deploy-heimdallr.sh -m
```

使用通用的多阶段构建Dockerfile，适用于任何架构，但可能在某些环境中前端构建会失败。

#### 方式三：指定架构

```bash
# 强制使用AMD64 Dockerfile
chmod +x deploy-heimdallr.sh
ARCH=x86_64 ./deploy-heimdallr.sh

# 强制使用ARM64 Dockerfile
chmod +x deploy-heimdallr.sh
ARCH=arm64 ./deploy-heimdallr.sh
```

脚本将执行以下操作：
- 构建Docker镜像（根据选择的方式和架构使用不同的Dockerfile）
- 启动容器
- 配置服务

### 4. 验证部署

部署完成后，可通过以下地址访问服务：

- 前端监控页面: http://localhost:8000
- 服务器API: http://localhost:8001
- 消费者API: http://localhost:8002
- 生产者API: http://localhost:8003

## 配置说明

### Dockerfile

项目提供四种Dockerfile：

#### 1. AMD64架构专用Dockerfile (Dockerfile.heimdallr-sdk.amd64)

位于`.tad/build/heimdallr-sdk/Dockerfile.heimdallr-sdk.amd64`，针对x86_64/AMD64架构优化：

1. **第一阶段**：构建前端
   - 基于Node.js 20镜像
   - 安装前端依赖
   - 使用esbuild-linux-64解决AMD64架构兼容性问题
   - 构建前端页面

2. **第二阶段**：构建后端
   - 基于Node.js 20镜像
   - 构建三个后端服务

3. **第三阶段**：创建最终镜像
   - 基于Node.js 20镜像
   - 从前两个阶段复制构建结果
   - 配置Nginx和Supervisor

#### 2. ARM64架构专用Dockerfile (Dockerfile.heimdallr-sdk.arm64)

位于`.tad/build/heimdallr-sdk/Dockerfile.heimdallr-sdk.arm64`，针对ARM64/AArch64架构优化：

1. **第一阶段**：构建前端
   - 基于Node.js 20镜像
   - 安装前端依赖
   - 使用esbuild-linux-arm64解决ARM64架构兼容性问题
   - 构建前端页面

2. **第二阶段**：构建后端
   - 基于Node.js 20镜像
   - 构建三个后端服务

3. **第三阶段**：创建最终镜像
   - 基于Node.js 20镜像
   - 从前两个阶段复制构建结果
   - 配置Nginx和Supervisor

#### 3. 多阶段构建Dockerfile (Dockerfile.multistage)

位于`.tad/build/heimdallr-sdk/Dockerfile.multistage`，通用多阶段构建：

1. **第一阶段**：构建前端
   - 基于Node.js 20镜像
   - 安装前端依赖
   - 尝试解决esbuild平台兼容性问题
   - 构建前端页面

2. **第二阶段**：构建后端
   - 基于Node.js 20镜像
   - 构建三个后端服务

3. **第三阶段**：创建最终镜像
   - 基于Node.js 20镜像
   - 从前两个阶段复制构建结果
   - 配置Nginx和Supervisor

#### 4. 简单版Dockerfile (Dockerfile.simple)

位于`.tad/build/heimdallr-sdk/Dockerfile.simple`，简化版构建：

1. 基于Node.js 20镜像
2. 安装必要的依赖
3. 构建后端服务
4. 创建前端占位页面（需要在本地构建前端并复制到容器）
5. 配置Nginx和Supervisor

### Nginx配置

Nginx配置文件位于`.tad/build/heimdallr-sdk/nginx.conf`，主要功能：

1. 提供前端静态文件服务（端口8000）
2. 代理API请求到后端服务
3. 处理前端路由

关键配置：

```nginx
# 代理到server服务
location /api/server/ {
    proxy_pass http://localhost:8001/;
}

# 代理到consumer服务
location /api/consumer/ {
    proxy_pass http://localhost:8002/;
}

# 代理到producer服务
location /api/producer/ {
    proxy_pass http://localhost:8003/;
}

# 默认API代理到server服务
location /api/ {
    proxy_pass http://localhost:8001/;
}
```

### Supervisor配置

Supervisor配置文件位于`.tad/build/heimdallr-sdk/supervisord.conf`，用于管理容器内的服务：

1. 启动Nginx
2. 启动Server服务
3. 启动Consumer服务
4. 启动Producer服务

## 常见问题

### 1. 容器启动失败

**问题**：容器无法正常启动或服务无法访问。

**解决方案**：
- 检查数据库连接配置
- 查看容器日志：`docker logs heimdallr-sdk`
- 确认端口是否被占用

### 2. 前端页面无法加载

**问题**：前端页面显示空白或报错。

**解决方案**：
- 检查Nginx配置
- 重新构建前端页面并更新容器
- 查看浏览器控制台错误

### 3. API请求失败

**问题**：前端页面无法正常请求API。

**解决方案**：
- 确认API基础URL配置正确（应为`/api`）
- 检查Nginx代理配置
- 确认后端服务正常运行

### 4. 数据库连接问题

**问题**：后端服务无法连接数据库。

**解决方案**：
- 确认数据库连接信息正确
- 检查数据库用户权限
- 确认数据库服务可访问

## 高级配置

### 自定义前端构建

#### 方式一：使用架构特定构建（推荐用于生产环境）

如果使用架构特定的Dockerfile，可以修改对应架构的Dockerfile中的前端构建配置：

```dockerfile
# 修改环境变量配置，确保API路径正确
RUN echo 'NODE_ENV=production\nVITE_API_URL=/api\nVITE_ASSET_URL=/' > .env.production
```

然后重新构建和部署：

```bash
# 自动检测架构
./deploy-heimdallr.sh

# 或指定架构
ARCH=x86_64 ./deploy-heimdallr.sh  # AMD64架构
ARCH=arm64 ./deploy-heimdallr.sh   # ARM64架构
```

#### 方式二：使用多阶段构建（适用于CI/CD环境）

如果使用多阶段构建，可以直接修改`Dockerfile.multistage`中的前端构建配置：

```dockerfile
# 修改环境变量配置，确保API路径正确
RUN echo 'NODE_ENV=production\nVITE_API_URL=/api\nVITE_ASSET_URL=/' > .env.production
```

然后重新构建和部署：

```bash
./deploy-heimdallr.sh -m
```

#### 方式三：本地构建（适用于开发调试）

如果容器内构建失败，可以在本地修改前端环境变量并构建：

1. 编辑`playground/manager/.env.production`文件：
   ```
   NODE_ENV=production
   VITE_API_URL=/api
   VITE_ASSET_URL=/
   ```

2. 重新构建前端：
   ```bash
   cd playground/manager
   pnpm install
   pnpm run build:private
   ```

3. 更新容器中的前端：
   ```bash
   docker cp dist/. heimdallr-sdk:/app/heimdallr-sdk/manager/
   docker restart heimdallr-sdk
   ```

### 自定义端口映射

如需修改端口映射，编辑`deploy-heimdallr.sh`脚本中的Docker运行命令：

```bash
docker run -d \
  --name heimdallr-sdk \
  -p 8000:8000 \
  -p 8001:8001 \
  -p 8002:8002 \
  -p 8003:8003 \
  ...
```

## 维护指南

### 容器管理

```bash
# 停止容器
docker stop heimdallr-sdk

# 启动容器
docker start heimdallr-sdk

# 重启容器
docker restart heimdallr-sdk

# 查看容器日志
docker logs heimdallr-sdk

# 进入容器
docker exec -it heimdallr-sdk bash
```

### 备份与恢复

**备份容器**：
```bash
# 备份容器为镜像
docker commit heimdallr-sdk heimdallr-sdk:backup-$(date +%Y%m%d)

# 导出镜像为文件
docker save heimdallr-sdk:backup-$(date +%Y%m%d) | gzip > heimdallr-sdk-backup-$(date +%Y%m%d).tar.gz
```

**恢复容器**：
```bash
# 导入镜像
gunzip -c heimdallr-sdk-backup-20230101.tar.gz | docker load

# 启动容器
docker run -d \
  --name heimdallr-sdk \
  -p 8000:8000 \
  -p 8001:8001 \
  -p 8002:8002 \
  -p 8003:8003 \
  heimdallr-sdk:backup-20230101
```

### 更新容器

1. 拉取最新代码：
   ```bash
   git pull
   ```

2. 重新构建并部署：
   ```bash
   ./deploy-heimdallr.sh -r
   ```

## 结语

通过本指南，您应该能够成功部署Heimdallr-SDK项目。如有任何问题，请参考项目文档或提交Issue。
