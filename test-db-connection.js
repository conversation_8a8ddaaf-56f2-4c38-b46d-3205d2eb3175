const mysql = require('mysql2/promise');

async function testConnection() {
  console.log('开始测试数据库连接...');

  const config = {
    host: '*************',
    port: 3306,
    user: 'root',
    password: 'fxy606775',
    database: 'heimdallr_sdk',
  };

  console.log('数据库配置:', {
    host: config.host,
    port: config.port,
    user: config.user,
    database: config.database,
  });

  try {
    console.log('尝试连接数据库...');
    const connection = await mysql.createConnection(config);
    console.log('数据库连接成功!');

    console.log('尝试查询数据库...');
    const [rows] = await connection.execute('SHOW DATABASES');
    console.log('数据库列表:');
    rows.forEach((row) => {
      console.log(`- ${row.Database}`);
    });

    await connection.end();
    console.log('数据库连接已关闭');
  } catch (error) {
    console.error('数据库连接失败:', error);
  }
}

testConnection();
