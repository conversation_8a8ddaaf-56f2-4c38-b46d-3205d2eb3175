{"name": "@heimdallr-sdk/server", "version": "0.0.19", "private": true, "description": "", "license": "MIT", "author": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/LuciferHuang/heimdallr-sdk/blob/main/packages/server/README.md", "repository": {"type": "git", "url": "git+https://github.com/LuciferHuang/heimdallr-sdk.git"}, "bugs": {"url": "https://github.com/LuciferHuang/heimdallr-sdk/issues"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "sdk", "server", "express", "monitor"], "publishConfig": {"access": "public"}, "scripts": {"dev": "ts-node src/index.ts", "build": "npm-run-all reset compile", "compile": "tsc", "reset": "rimraf dist/*", "start": "NODE_ENV=production ts-node src/index.ts"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-formidable": "^1.2.0", "express-ip": "^1.0.4", "formidable": "^3.5.2", "mysql2": "^2.3.3", "nanoid": "^3.3.6", "portfinder": "^1.0.32", "reflect-metadata": "^0.1.14", "source-map": "^0.7.4", "typeorm": "^0.3.23"}, "devDependencies": {"@types/browser-sync": "^2.26.3", "@types/express": "^4.17.13", "@types/express-formidable": "^1.2.0", "browser-sync": "^2.29.3", "npm-run-all": "^4.1.5", "rimraf": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^4.9.5"}}