<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>heimdallr-sdk DEMO</title>
    <script src="/customer-dist/customer.iife.js"></script>
    <script src="/dom-dist/dom.iife.js"></script>
    <script src="/fetch-dist/fetch.iife.js"></script>
    <script src="/hash-dist/hash.iife.js"></script>
    <script src="/history-dist/history.iife.js"></script>
    <script src="/crash-dist/page_crash.iife.js"></script>
    <script src="/performance-dist/performance.iife.js"></script>
    <script src="/xhr-dist/xhr.iife.js"></script>
    <script src="/record-dist/record.iife.js"></script>
    <script async src="/browser-dist/browser.iife.js"></script>
    <script>
      window.state = { a: { user: { name: 'test' } } };
      window.__HEIMDALLR_OPTIONS__ = {
        dsn: {
          host: 'localhost:8001',
          init: '/project/init',
          report: '/log/report'
        },
        app: {
          name: 'playgroundAPP',
          leader: 'test',
          desc: 'test proj'
        },
        plugins: [
          HEIMDALLR_CUSTOMER,
          HEIMDALLR_DOM,
          HEIMDALLR_FETCH,
          HEIMDALLR_HASH,
          HEIMDALLR_HISTORY,
          HEIMDALLR_PAGE_CRASH,
          HEIMDALLR_PERFORMANCE,
          HEIMDALLR_XHR,
          HEIMDALLR_RECORD
        ],
        userIdentify: {
          name: 'state.a.user.name',
          position: 'global'
        },
        customers: [
          {
            name: 'code',
            position: 'local'
          }
        ],
        pageCrashWorkerUrl: '/crash-worker/page_crash_worker.iife.js',
        ignoreUrls: ['http://localhost:8001/browser-sync/socket.io'], // 过滤无需监控的api
        debug: true
      };
    </script>
  </head>
  <body>
    <button id="TypeError">值不是所期待的类型</button>
    <hr />
    <button id="ReferenceError">引用未声明的变量</button>
    <hr />
    <button id="promiseError">promise错误</button>
    <hr />
    <button id="fetchDom">fetch发送请求</button>
    <hr />
    <button id="xhrDom">XHR发送请求</button>
    <hr />
    <button id="crash">触发页面崩溃</button>
    <hr />
    <button id="customer">自定义上报</button>
    <hr />
    <button>Click Here</button>
    <hr />
    <div id="hello"></div>
    <script>
      const pageCrashDom = document.querySelector('#crash');
      const TypeError = document.querySelector('#TypeError');
      const ReferenceError = document.querySelector('#ReferenceError');
      const promiseError = document.querySelector('#promiseError');
      const fetchDom = document.querySelector('#fetchDom');
      const xhrDom = document.querySelector('#xhrDom');
      const customerDom = document.querySelector('#customer');

      pageCrashDom.addEventListener('click', () => {
        // 页面崩溃脚本
        setTimeout(() => {
          var obj;
          // JavaScript对象obj到DOM对象的引用，根据id获得
          for (; 1; ) {
            obj = document.getElementById('hello'); // DOM 对象则有到此 JavaScript 对象的引用，由expandoProperty实现
            document.getElementById('hello').expandoProperty = obj;
          }
        }, 1000);
      });

      TypeError.addEventListener('click', () => {
        const person = void 0;
        person.name;
      });

      ReferenceError.addEventListener('click', () => {
        console.log(notdefined);
      });

      promiseError.addEventListener('click', () => {
        new Promise((resolve, reject) => {
          JSON.parse('');
          resolve();
        });
      });

      customerDom.addEventListener('click', () => {
        HEIMDALLR_REPORT('customer-click', 'funny');
      });

      fetchDom.addEventListener('click', () => {
        fetch('/test');
      });

      xhrDom.addEventListener('click', () => {
        var xhr = new XMLHttpRequest();
        xhr.open('GET', '/test');
        xhr.send();
        xhr.onreadystatechange = function () {
          if (xhr.readyState === 4 && xhr.status === 200) {
            // 获取服务器响应的数据
            console.log(xhr.responseText);
          }
        };
      });
    </script>
  </body>
</html>
