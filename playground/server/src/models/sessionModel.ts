import { getConnection } from '../config/database';
import { Session } from '../entities';
import { ConditionType, ModelResponseType } from '../types';

class SessionModel {
  async add(datas: any[]): Promise<ModelResponseType<any>> {
    try {
      const connection = getConnection();
      const sessionRepository = connection.getRepository(Session);

      // 创建实体对象并保存
      const sessions = datas.map((data) => {
        const session = new Session();
        session.id = data.id;
        session.user_id = data.user_id;
        session.account = data.account;
        session.ip = data.ip;
        session.province = data.province;
        session.path = data.path;
        session.page_title = data.page_title;
        session.platform = data.platform;
        session.stay_time = data.stay_time;
        session.terminal = data.terminal;
        session.language = data.language;
        session.events = data.events;
        session.breadcrumb = data.breadcrumb;
        session.user_agent = data.user_agent;
        session.window_size = data.window_size;
        session.document_size = data.document_size;
        session.etime = new Date(data.etime);
        session.ltime = new Date(data.ltime);
        return session;
      });

      // 使用 save 方法保存数据
      await sessionRepository.save(sessions, { chunk: 50 });

      return {
        status: true,
        msg: 'success'
      };
    } catch (error) {
      return {
        status: false,
        msg: error.message || String(error)
      };
    }
  }

  async modify(query, data: any): Promise<ModelResponseType<any>> {
    try {
      const connection = getConnection();
      const sessionRepository = connection.getRepository(Session);

      // 查找要更新的记录
      const session = await sessionRepository.findOne({ where: query });

      if (!session) {
        throw new Error('Session not found');
      }

      // 更新记录
      Object.keys(data).forEach(key => {
        if (key === 'etime' || key === 'ltime') {
          session[key] = new Date(data[key]);
        } else {
          session[key] = data[key];
        }
      });

      // 保存更新
      const result = await sessionRepository.save(session);

      return {
        status: true,
        data: result,
        msg: 'success'
      };
    } catch (error) {
      return {
        status: false,
        msg: error.message || String(error)
      };
    }
  }

  async count(query = {}): Promise<ModelResponseType<number>> {
    try {
      const connection = getConnection();
      const sessionRepository = connection.getRepository(Session);

      const result = await sessionRepository.count({ where: query });

      return {
        status: true,
        data: result,
        msg: 'success'
      };
    } catch (error) {
      return {
        status: false,
        msg: error.message || String(error)
      };
    }
  }

  async find(pindex: number = 1, psize: number = 1, query = {}, order?): Promise<ModelResponseType<any[]>> {
    try {
      const connection = getConnection();
      const sessionRepository = connection.getRepository(Session);

      const skip = (pindex - 1) * Number(psize);
      const take = Number(psize);

      // 构建查询选项
      const options: any = { where: query };

      if (skip && psize) {
        options.skip = skip;
        options.take = take;
      }

      if (order) {
        options.order = order;
      }

      const result = await sessionRepository.find(options);

      return {
        status: true,
        data: result,
        msg: 'success'
      };
    } catch (error) {
      return {
        status: false,
        data: [],
        msg: error.message || String(error)
      };
    }
  }
}

export default SessionModel;
