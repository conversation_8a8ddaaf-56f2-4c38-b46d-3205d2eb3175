import { getConnection } from '../config/database';
import { BreadCrumb } from '../entities';
import { ModelResponseType, BreadCrumbRes } from '../types';

class BreadCrumbModel {
  async add(datas: BreadCrumbRes[]): Promise<ModelResponseType<BreadCrumbRes>> {
    try {
      const connection = getConnection();
      const breadcrumbRepository = connection.getRepository(BreadCrumb);

      // 创建实体对象并保存
      const breadcrumbs = datas.map((data) => {
        const breadcrumb = new BreadCrumb();
        breadcrumb.id = data.id;
        breadcrumb.event_id = data.event_id;
        breadcrumb.type = parseInt(data.type);
        breadcrumb.message = data.message;
        breadcrumb.level = parseInt(data.level);
        breadcrumb.time = new Date(data.time);
        return breadcrumb;
      });

      // 使用 save 方法保存数据
      await breadcrumbRepository.save(breadcrumbs, { chunk: 50 });

      return {
        status: true,
        msg: 'success'
      };
    } catch (error) {
      return {
        status: false,
        msg: error.message || String(error)
      };
    }
  }

  async find(query = {}): Promise<ModelResponseType<any[]>> {
    try {
      const connection = getConnection();
      const breadcrumbRepository = connection.getRepository(BreadCrumb);

      const result = await breadcrumbRepository.find({
        where: query,
        order: {
          time: 'DESC'
        }
      });

      return {
        status: true,
        data: result,
        msg: 'success'
      };
    } catch (error) {
      return {
        status: false,
        data: [],
        msg: error.message || String(error)
      };
    }
  }
}

export default BreadCrumbModel;
