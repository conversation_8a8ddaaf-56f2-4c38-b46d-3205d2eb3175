import { getConnection } from '../config/database';
import { Log } from '../entities';
import { IAnyObject, LogItem, ModelResponseType } from '../types';

interface LogCondition {
  where?: IAnyObject;
  order?: IAnyObject;
  skip?: number;
  take?: number;
}

class LogModel {
  async add(datas: LogItem[]): Promise<ModelResponseType<LogItem>> {
    try {
      const connection = getConnection();
      const logRepository = connection.getRepository(Log);

      // 创建实体对象并保存
      const logs = datas.map((data) => {
        const log = new Log();
        if (data.id) log.id = data.id;
        log.otime = new Date(data.otime);
        log.type = data.type;
        log.sub_type = parseInt(data.sub_type as string);
        log.session_id = data.session_id;
        log.ascription_id = data.ascription_id;
        log.data = data.data;
        log.platform = data.platform;
        log.path = data.path;
        return log;
      });

      // 使用 save 方法保存数据
      await logRepository.save(logs, { chunk: 50 });

      return {
        status: true,
        msg: 'success',
      };
    } catch (error) {
      return {
        status: false,
        msg: error.message || String(error),
      };
    }
  }

  async modify(query, data: LogItem): Promise<ModelResponseType<LogItem>> {
    try {
      const connection = getConnection();
      const logRepository = connection.getRepository(Log);

      // 查找要更新的记录
      const log = await logRepository.findOne({ where: query });

      if (!log) {
        throw new Error('Log not found');
      }

      // 更新记录
      if (data.otime) log.otime = new Date(data.otime);
      if (data.type) log.type = data.type;
      if (data.sub_type) log.sub_type = parseInt(data.sub_type as string);
      if (data.session_id) log.session_id = data.session_id;
      if (data.ascription_id) log.ascription_id = data.ascription_id;
      if (data.data) log.data = data.data;
      if (data.platform) log.platform = data.platform;
      if (data.path) log.path = data.path;

      // 保存更新
      const result = await logRepository.save(log);

      return {
        status: true,
        data: {
          id: result.id,
          otime: result.otime,
          type: result.type,
          sub_type: result.sub_type.toString(),
          session_id: result.session_id,
          ascription_id: result.ascription_id,
          data: result.data,
          platform: result.platform,
          path: result.path,
        },
        msg: 'success',
      };
    } catch (error) {
      return {
        status: false,
        msg: error.message || String(error),
      };
    }
  }

  async count(query = {}): Promise<ModelResponseType<number>> {
    try {
      const connection = getConnection();
      const logRepository = connection.getRepository(Log);

      const result = await logRepository.count({ where: query });

      return {
        status: true,
        data: result,
        msg: 'success',
      };
    } catch (error) {
      return {
        status: false,
        msg: error.message || String(error),
      };
    }
  }

  async find(
    pindex: number = 1,
    psize: number = 1,
    query = {},
    order?
  ): Promise<ModelResponseType<LogItem[]>> {
    try {
      const connection = getConnection();
      const logRepository = connection.getRepository(Log);

      const skip = (pindex - 1) * Number(psize);
      const take = Number(psize);

      // 构建查询选项
      const options: LogCondition = { where: query };

      if (skip && psize) {
        options.skip = skip;
        options.take = take;
      }

      if (order) {
        options.order = order;
      }

      const result = await logRepository.find(options);

      // 转换为 LogItem 格式
      const logItems = result.map((log) => ({
        id: log.id,
        otime: log.otime,
        type: log.type,
        sub_type: log.sub_type.toString(),
        session_id: log.session_id,
        ascription_id: log.ascription_id,
        data: log.data,
        platform: log.platform,
        path: log.path,
      }));

      return {
        status: true,
        data: logItems,
        msg: 'success',
      };
    } catch (error) {
      return {
        status: false,
        data: [],
        msg: error.message || String(error),
      };
    }
  }
}

export default LogModel;
