/*
 * @Author: lucyfang
 * @Date: 2025-05-13 19:04:10
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-05-14 14:14:11
 * @Description: 请输入注释信息
 */
import { DataSource } from 'typeorm';
import { Project, Session, Log, BreadCrumb } from '../entities';
import * as path from 'path';
import * as fs from 'fs';

// 获取数据库配置
const getDbConfig = () => {
  try {
    // 尝试从挂载的配置文件读取
    const configPath = path.resolve(process.cwd(), 'config/db.js');
    if (fs.existsSync(configPath)) {
      const dbConfig = require(configPath);
      return {
        type: 'mysql',
        host: dbConfig.dbHostInfo?.host || dbConfig.host,
        port: dbConfig.dbHostInfo?.port || dbConfig.port,
        username: dbConfig.uname,
        password: dbConfig.upwd,
        database: dbConfig.dbname,
      };
    }
  } catch (error) {
    console.error('读取挂载的数据库配置失败:', error);
  }

  // 使用环境变量配置
  return {
    type: 'mysql',
    host: process.env.DB_HOST || '*************',
    port: parseInt(process.env.DB_PORT || '3306'),
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'fxy606775',
    database: process.env.DB_NAME || 'heimdallr_sdk',
  };
};

// 创建数据库连接
const dbConfig = getDbConfig();
console.log('dbConfig', dbConfig);
console.log('数据库配置:', {
  type: dbConfig.type,
  host: dbConfig.host,
  port: dbConfig.port,
  username: dbConfig.username,
  database: dbConfig.database,
});
export const AppDataSource = new DataSource({
  type: dbConfig.type as 'mysql',
  host: dbConfig.host,
  port: dbConfig.port,
  username: dbConfig.username,
  password: dbConfig.password,
  database: dbConfig.database,
  synchronize: false, // 生产环境不要设置为 true
  logging: process.env.NODE_ENV !== 'production',
  entities: [Project, Session, Log, BreadCrumb],
  connectTimeout: 30000, // 连接超时时间，30秒
  extra: {
    connectionLimit: 10, // 连接池最大连接数
  },
});

// 初始化数据库连接
export const initializeDatabase = async () => {
  try {
    // 如果已经初始化，则直接返回
    if (AppDataSource.isInitialized) {
      console.log('数据库连接已经初始化，无需重新初始化');
      return AppDataSource;
    }

    console.log('正在初始化数据库连接...');
    await AppDataSource.initialize();
    console.log('数据库连接已初始化成功');
    return AppDataSource;
  } catch (error) {
    console.error('数据库连接初始化失败:', error);

    // 尝试重新连接
    console.log('尝试重新连接数据库...');
    try {
      if (AppDataSource.isInitialized) {
        await AppDataSource.destroy();
      }
      await AppDataSource.initialize();
      console.log('数据库重新连接成功');
      return AppDataSource;
    } catch (retryError) {
      console.error('数据库重新连接失败:', retryError);
      throw retryError;
    }
  }
};

// 获取数据库连接
export const getConnection = () => {
  if (!AppDataSource.isInitialized) {
    throw new Error('数据库连接尚未初始化');
  }
  return AppDataSource;
};

// 关闭数据库连接
export const closeDatabase = async () => {
  if (AppDataSource.isInitialized) {
    await AppDataSource.destroy();
    console.log('数据库连接已关闭');
  }
};
