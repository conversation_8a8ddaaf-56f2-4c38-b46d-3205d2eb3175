/*
 * @Author: lucyfang
 * @Date: 2024-12-09 10:38:07
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-05-26 16:25:54
 * @Description: 请输入注释信息
 */

import express from 'express';
import cors from 'cors';
import formidable from 'express-formidable';
import { create } from 'browser-sync';
import router from './route';
import expressIp from 'express-ip';
import { getUseablePort } from './lib/utils';
import { initializeDatabase } from './config/database';

require('dotenv').config();

const app = express();

app.use(formidable());
app.use(
  cors({
    origin: [
      'https://jiguang.woa.com',
      'http://jiguang.woa.com',
      'http://dev.jiguang.woa.com',
      'https://pre.jiguang.woa.com',
      'https://tcsc.woa.com:20443',
      'http://tcsc.woa.com:20080',
      'https://tcsc.woa.com',
      'http://tcsc.woa.com',
      // 本地开发环境
      'http://localhost:3000',
      'http://localhost:8000',
      'http://localhost:8080',
    ],
    credentials: true,
    exposedHeaders: 'date',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'Accept',
      'Origin',
    ],
  })
);
app.use(expressIp().getIpInfoMiddleware);
app.use(router);

const bs = create();

// 启动服务器函数
const startServer = () => {
  console.log('启动服务器...');

  if (process.env.NODE_ENV === 'production') {
    app.listen(8001, () => {
      console.log(`服务器已启动，监听端口 8001`);
    });
  } else {
    Promise.all([getUseablePort(), getUseablePort()]).then(([port, proxyPort]) => {
      if (!port || !proxyPort) {
        console.error('无法获取可用端口');
        return;
      }
      app.listen(port, () => {
        console.log(`服务器已启动，监听端口 ${port}`);
        bs.init({
          open: false,
          ui: false,
          notify: true,
          proxy: `localhost:${port}`,
          files: ['packages/**/dist/*.iife.js'],
          port: proxyPort,
        });
      });
    });
  }
};

// 尝试初始化数据库连接
console.log('尝试连接数据库...');
initializeDatabase()
  .then(() => {
    console.log('数据库连接成功');
    startServer();
  })
  .catch((error) => {
    console.error('数据库连接失败:', error);

    // 即使数据库连接失败，也尝试启动服务器
    console.log('尽管数据库连接失败，仍然尝试启动服务器...');
    try {
      startServer();
    } catch (serverError) {
      console.error('启动服务器失败:', serverError);
      process.exit(1);
    }
  });
