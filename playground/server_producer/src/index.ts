/*
 * @Author: lucyfang
 * @Date: 2024-12-09 10:38:07
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-05-13 14:35:03
 * @Description: 请输入注释信息
 */
import express from 'express';
import cors from 'cors';
import formidable from 'express-formidable';
import { create } from 'browser-sync';
import router from './route';
import expressIp from 'express-ip';
import { getUseablePort } from './lib/utils';
import { initializeDatabase } from './config/database';

require('dotenv').config();
const app = express();

app.use(formidable());
app.use(
  cors({
    origin: [
      'https://jiguang.woa.com',
      'http://jiguang.woa.com',
      'http://dev.jiguang.woa.com',
      'https://pre.jiguang.woa.com',
      'https://tcsc.woa.com:20443',
      'http://tcsc.woa.com:20080',
      'https://tcsc.woa.com',
      'http://tcsc.woa.com',
      // 本地开发环境
      'http://localhost:3000',
      'http://localhost:8000',
      'http://localhost:8080',
    ],
    credentials: true,
    exposedHeaders: 'date',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'Accept',
      'Origin',
    ],
  })
);
app.use(expressIp().getIpInfoMiddleware);
app.use(router);

// 初始化数据库连接
initializeDatabase().catch((err) => {
  console.error('数据库连接失败:', err);
  process.exit(1);
});

const bs = create();

if (process.env.NODE_ENV === 'production') {
  app.listen(8003, () => {});
} else {
  Promise.all([getUseablePort(), getUseablePort()]).then(([port, proxyPort]) => {
    if (!port || !proxyPort) {
      return;
    }
    app.listen(port, () => {
      bs.init({
        open: false,
        ui: false,
        notify: true,
        proxy: `localhost:${port}`,
        files: ['packages/**/dist/*.iife.js'],
        port: proxyPort,
      });
    });
  });
}
