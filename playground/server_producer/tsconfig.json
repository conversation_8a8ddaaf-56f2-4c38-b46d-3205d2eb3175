{
  "compilerOptions": {
    "target": "esnext", // 目标语言版本
    "module": "commonjs", // 指定生成代码的模板标准
    "sourceMap": false,
    "outDir": "./dist",
    "rootDir": "./src", // 指定输出目录, 默认是dist文件夹
    "strict": false,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true
  },
  // 需要编译的的文件和目录
  "include": ["src"],
  "exclude": ["node_modules", "dist", "public"]
}
