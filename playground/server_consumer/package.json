{"name": "@heimdallr-sdk/server-consumer", "version": "0.0.11", "private": true, "description": "", "license": "MIT", "author": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/LuciferHuang/heimdallr-sdk/blob/main/packages/server_consumer/README.md", "repository": {"type": "git", "url": "git+https://github.com/LuciferHuang/heimdallr-sdk.git"}, "bugs": {"url": "https://github.com/LuciferHuang/heimdallr-sdk/issues"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "sdk", "server", "consumer", "rabbitMQ", "express", "monitor"], "publishConfig": {"access": "public"}, "scripts": {"dev": "ts-node src/index.ts", "build": "npm-run-all reset compile", "compile": "tsc", "reset": "rimraf dist/*"}, "dependencies": {"amqplib": "^0.10.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.18.1", "express-formidable": "^1.2.0", "mysql2": "^3.14.1", "portfinder": "^1.0.32", "source-map": "^0.7.4", "typeorm": "^0.3.23"}, "devDependencies": {"@types/amqplib": "^0.10.1", "@types/browser-sync": "^2.26.3", "@types/express": "^4.17.13", "@types/express-formidable": "^1.2.0", "browser-sync": "^2.27.11", "npm-run-all": "^4.1.5", "rimraf": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^4.9.5"}}