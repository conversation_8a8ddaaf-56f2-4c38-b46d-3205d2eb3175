import { ConditionType, ModelResponseType, ProjItem } from '../types';
import { getConnection } from '../config/database';
import { Project } from '../entities';

class ProjModel {
  async add(datas: ProjItem[]): Promise<ModelResponseType<ProjItem>> {
    try {
      const connection = getConnection();
      const projectRepository = connection.getRepository(Project);

      // 创建实体对象并保存
      const projects = datas.map((data) => {
        const project = new Project();
        project.id = data.id;
        project.name = data.name;
        project.leader = data.leader;
        project.desc = data.desc || '';
        project.ctime = new Date(data.ctime);
        return project;
      });

      // 使用 save 方法保存数据，skipDuplicates 通过捕获错误实现
      await projectRepository.save(projects, { chunk: 50 });

      return {
        status: true,
        msg: 'success',
      };
    } catch (error) {
      return {
        status: false,
        msg: error.message || String(error),
      };
    }
  }

  async count(query = {}): Promise<ModelResponseType<number>> {
    try {
      const connection = getConnection();
      const projectRepository = connection.getRepository(Project);

      const count = await projectRepository.count({ where: query });

      return {
        status: true,
        data: count,
        msg: 'success',
      };
    } catch (error) {
      return {
        status: false,
        msg: error.message || String(error),
      };
    }
  }

  async find(
    pindex: number = 1,
    psize: number = 1,
    query = {},
    order?
  ): Promise<ModelResponseType<ProjItem[]>> {
    try {
      const connection = getConnection();
      const projectRepository = connection.getRepository(Project);

      const skip = (pindex - 1) * Number(psize);
      const take = Number(psize);

      // 构建查询选项
      const options: any = { where: query };

      if (skip && psize) {
        options.skip = skip;
        options.take = take;
      }

      if (order) {
        options.order = order;
      }

      const result = await projectRepository.find(options);

      // 转换为 ProjItem 格式
      const projItems = result.map((project) => ({
        id: project.id,
        name: project.name,
        leader: project.leader,
        desc: project.desc,
        ctime: project.ctime.toISOString(),
      }));

      return {
        status: true,
        data: projItems,
        msg: 'success',
      };
    } catch (error) {
      return {
        status: false,
        data: [],
        msg: error.message || String(error),
      };
    }
  }
}

export default ProjModel;
