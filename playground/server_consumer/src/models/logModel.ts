import { IAnyObject, LogItem, ModelResponseType } from '../types';
import { getConnection } from '../config/database';
import { Log } from '../entities';

interface LogCodition {
  where?: IAnyObject;
  orderBy?: any[];
  skip?: number;
  take?: number;
}

class LogModel {
  async add(datas: LogItem[]): Promise<ModelResponseType<LogItem>> {
    try {
      const connection = getConnection();
      const logRepository = connection.getRepository(Log);

      // 创建实体对象并保存
      const logs = datas.map((data) => {
        const log = new Log();
        log.id = data.id;
        log.otime = new Date(data.otime);
        log.type = data.type;
        log.sub_type =
          typeof data.sub_type === 'string'
            ? parseInt(data.sub_type)
            : data.sub_type;
        log.session_id = data.session_id;
        log.ascription_id = data.ascription_id;
        log.data = data.data;
        log.platform = data.platform;
        log.path = data.path;
        return log;
      });

      // 使用 save 方法保存数据
      await logRepository.save(logs, { chunk: 50 });

      return {
        status: true,
        msg: 'success',
      };
    } catch (error) {
      return {
        status: false,
        msg: error.message || String(error),
      };
    }
  }

  async modify(query, data: LogItem): Promise<ModelResponseType<LogItem>> {
    try {
      const connection = getConnection();
      const logRepository = connection.getRepository(Log);

      // 查找并更新记录
      const log = await logRepository.findOne({ where: query });
      if (!log) {
        throw new Error('Log not found');
      }

      // 更新字段
      Object.assign(log, data);

      // 保存更新
      const result = await logRepository.save(log);

      return {
        status: true,
        data: result as any,
        msg: 'success',
      };
    } catch (error) {
      return {
        status: false,
        msg: error.message || String(error),
      };
    }
  }

  async count(query = {}): Promise<ModelResponseType<number>> {
    try {
      const connection = getConnection();
      const logRepository = connection.getRepository(Log);

      const count = await logRepository.count({ where: query });

      return {
        status: true,
        data: count,
        msg: 'success',
      };
    } catch (error) {
      return {
        status: false,
        msg: error.message || String(error),
      };
    }
  }

  async find(
    pindex: number = 1,
    psize: number = 1,
    query = {},
    order?
  ): Promise<ModelResponseType<LogItem[]>> {
    try {
      const connection = getConnection();
      const logRepository = connection.getRepository(Log);

      const skip = (pindex - 1) * Number(psize);
      const take = Number(psize);

      // 构建查询选项
      const options: any = { where: query };

      if (order) {
        options.order = order;
      }

      if (pindex && psize) {
        options.skip = skip;
        options.take = take;
      }

      const result = await logRepository.find(options);

      return {
        status: true,
        data: result as any[],
        msg: 'success',
      };
    } catch (error) {
      return {
        status: false,
        data: [],
        msg: error.message || String(error),
      };
    }
  }
}

export default LogModel;
