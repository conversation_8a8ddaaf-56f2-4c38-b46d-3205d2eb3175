import { ModelResponseType, BreadCrumbRes } from '../types';
import { getConnection } from '../config/database';
import { BreadCrumb } from '../entities';

class BreadCrumbModel {
  async add(datas: BreadCrumbRes[]): Promise<ModelResponseType<BreadCrumbRes>> {
    try {
      const connection = getConnection();
      const breadCrumbRepository = connection.getRepository(BreadCrumb);

      // 创建实体对象并保存
      const breadcrumbs = datas.map((data) => {
        const breadcrumb = new BreadCrumb();
        breadcrumb.id = data.id;
        breadcrumb.event_id = data.event_id;
        breadcrumb.type =
          typeof data.type === 'string' ? parseInt(data.type) : data.type;
        breadcrumb.message = data.message;
        breadcrumb.level =
          typeof data.level === 'string' ? parseInt(data.level) : data.level;
        breadcrumb.time = new Date(data.time);
        return breadcrumb;
      });

      // 使用 save 方法保存数据
      await breadCrumbRepository.save(breadcrumbs, { chunk: 50 });

      return {
        status: true,
        msg: 'success',
      };
    } catch (error) {
      return {
        status: false,
        msg: error.message || String(error),
      };
    }
  }

  async find(query = {}): Promise<ModelResponseType<BreadCrumbRes[]>> {
    try {
      const connection = getConnection();
      const breadCrumbRepository = connection.getRepository(BreadCrumb);

      // 构建查询选项
      const options: any = {
        where: query,
        order: {
          time: 'DESC',
        },
      };

      const result = await breadCrumbRepository.find(options);

      return {
        status: true,
        data: result as any[],
        msg: 'success',
      };
    } catch (error) {
      return {
        status: false,
        data: [],
        msg: error.message || String(error),
      };
    }
  }
}

export default BreadCrumbModel;
