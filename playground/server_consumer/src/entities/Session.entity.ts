import { <PERSON><PERSON>ty, Column, PrimaryColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('Session')
export class Session {
  @PrimaryColumn()
  id: string;

  @Column()
  user_id: string;

  @Column()
  account: string;

  @Column()
  ip: string;

  @Column()
  province: string;

  @Column()
  path: string;

  @Column()
  page_title: string;

  @Column()
  platform: string;

  @Column()
  stay_time: number;

  @Column()
  terminal: string;

  @Column()
  language: string;

  @Column('longtext')
  events: string;

  @Column('longtext')
  breadcrumb: string;

  @Column({ length: 255 })
  user_agent: string;

  @Column()
  window_size: string;

  @Column()
  document_size: string;

  @Column()
  etime: Date;

  @Column()
  ltime: Date;

  @CreateDateColumn()
  @UpdateDateColumn()
  ctime: Date;
}
