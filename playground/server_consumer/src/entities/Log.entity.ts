import { <PERSON><PERSON>ty, Column, PrimaryColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('Log')
export class Log {
  @PrimaryColumn()
  id: string;

  @CreateDateColumn()
  @UpdateDateColumn()
  ctime: Date;

  @Column()
  otime: Date;

  @Column()
  type: string;

  @Column()
  sub_type: number;

  @Column()
  session_id: string;

  @Column()
  ascription_id: string;

  @Column('longtext')
  data: string;

  @Column()
  platform: string;

  @Column()
  path: string;
}
