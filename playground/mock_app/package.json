{"name": "@heimdallr-sdk/mock-app", "version": "0.0.4", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heimdallr-sdk/browser": "workspace:^", "@heimdallr-sdk/core": "workspace:^", "@heimdallr-sdk/dom": "workspace:^", "@heimdallr-sdk/fetch": "workspace:^", "@heimdallr-sdk/hash": "workspace:^", "@heimdallr-sdk/history": "workspace:^", "@heimdallr-sdk/page-crash": "workspace:^", "@heimdallr-sdk/performance": "workspace:^", "@heimdallr-sdk/record": "workspace:^", "@heimdallr-sdk/types": "workspace:^", "@heimdallr-sdk/utils": "workspace:^", "@heimdallr-sdk/xhr": "workspace:^", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2"}, "devDependencies": {"@eslint/js": "^9.9.0", "@heimdallr-sdk/vite-plugin-sourcemap-upload": "workspace:^", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "typescript": "^4.9.5", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}