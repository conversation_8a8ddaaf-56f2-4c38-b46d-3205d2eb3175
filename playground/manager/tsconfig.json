{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": false, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "experimentalDecorators": true, "lib": ["esnext", "dom"], "baseUrl": "./", "paths": {"@/*": ["./src/*"], "components/*": ["./src/components/*"], "helper/*": ["./src/helper/*"], "config/*": ["./src/helper/config/*"]}}, "include": ["./src/**/*.ts", "./src/**/*.d.ts", "./src/**/*.tsx", "./src/**/*.vue"], "exclude": ["node_modules"]}