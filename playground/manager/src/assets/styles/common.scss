// 通用边距样式（1px到50px）
@for $i from 0 through 50 {
  .mg-#{$i} {
    margin: $i + px;
  }
  .mg-t-#{$i} {
    margin-top: $i + px;
  }
  .mg-b-#{$i} {
    margin-bottom: $i + px;
  }
  .mg-l-#{$i} {
    margin-left: $i + px;
  }
  .mg-r-#{$i} {
    margin-right: $i + px;
  }
  .pd-#{$i} {
    padding: $i + px;
  }
  .pd-t-#{$i} {
    padding-top: $i + px;
  }
  .pd-b-#{$i} {
    padding-bottom: $i + px;
  }
  .pd-l-#{$i} {
    padding-left: $i + px;
  }
  .pd-r-#{$i} {
    padding-right: $i + px;
  }
}
/*文本格式化，超出范围，显示省略号*/
@mixin text-overflow($width: 100%, $line: 1) {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -weblit-box-orient: vertical;
  width: $width;
  -weblit-line-clamp: $line;
}
/**
 *默认flex样式水平，垂直居中
 *	1、$direction:... -- 水平排/垂直排
 *	2、$justify:... -- 主轴上对齐方式
 *  3、$align:... -- 侧轴上对齐方式（默认纵轴）
 *  4、$flex-wrap:... -- 容器是单行或者多行
 */
@mixin flex-center($direction: row, $justify: center, $align: null, $flex-wrap: null) {
  display: flex;
  @if ($flex-wrap != null) {
    flex-wrap: $flex-wrap;
  }
  @if ($direction!=null) {
    flex-direction: $direction;
  }
  @if ($justify!=null) {
    justify-content: $justify;
  }
  @if ($align!=null) {
    align-items: $align;
  }
}
/*绝对定位  参数顺序：上右下左*/
@mixin positionAbsolute($top: null, $right: null, $bottom: null, $left: null) {
  position: absolute;
  @if ($left!= "" & & $left!=null) {
    left: $left;
  }
  @if ($right!= "" & & $right!=null) {
    right: $right;
  }
  @if ($top!= "" & & $top!=null) {
    top: $top;
  }
  @if ($bottom!= "" & & $bottom!=null) {
    bottom: $bottom;
  }
}
