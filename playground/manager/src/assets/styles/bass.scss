@charset "utf-8";
* {
  box-sizing: border-box;
}
html,
body {
  padding: 0;
  margin: 0;
}
ul {
  padding-left: 3px;
}
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}
.el-sub-menu__title {
  padding-right: unset;
}
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: #f5f5f5;
}
/*定义滚动条的轨道，内阴影及圆角*/
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 5px;
  background-color: #f5f5f5;
}
/*定义滑块，内阴影及圆角*/
::-webkit-scrollbar-thumb {
  height: 10px;
  border-radius: 5px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #555;
}

code {
  padding: 0!important;
}
