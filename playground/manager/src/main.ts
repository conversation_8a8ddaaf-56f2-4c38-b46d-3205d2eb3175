/*
 * @Author: lucyfang
 * @Date: 2024-12-09 10:38:07
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-05-14 17:26:48
 * @Description: 请输入注释信息
 */
import 'element-plus/dist/index.css';
import '@/assets/styles/index.scss';
import 'highlight.js/lib/common';
import 'highlight.js/scss/xcode.scss';
import Hljs from '@highlightjs/vue-plugin';
import { createApp } from 'vue';
import App from '@/App.vue';
import { router } from '@/route/index';

const app = createApp(App);
app.use(Hljs);
app.use(router).mount('#app');
