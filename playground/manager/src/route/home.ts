/*
 * @Author: lucyfang
 * @Date: 2024-12-09 10:38:07
 * @LastEditors: lucyfang
 * @LastEditTime: 2025-05-14 17:45:07
 * @Description: 请输入注释信息
 */
const overview = () => import('@/pages/overview/index.vue');
const overviewBasic = () => import('@/pages/overview/basic.vue');
const projects = () => import('@/pages/projects/index.vue');
const projectsList = () => import('@/pages/projects/list.vue');
const session = () => import('@/pages/sessions/index.vue');
const sessionList = () => import('@/pages/sessions/list.vue');
const log = () => import('@/pages/log/index.vue');
const errorsList = () => import('@/pages/log/list.vue');

const homeRoutes = [
  {
    name: 'view',
    path: 'view',
    component: overview,
    redirect: '/home/<USER>/basic',
    children: [
      {
        path: 'basic',
        component: overviewBasic,
      },
    ],
  },
  {
    name: 'projects',
    path: 'projects',
    component: projects,
    redirect: '/home/<USER>/list',
    children: [
      {
        path: 'list',
        component: projectsList,
      },
    ],
  },
  {
    name: 'session',
    path: 'session',
    component: session,
    redirect: '/home/<USER>/list',
    children: [
      {
        path: 'list',
        component: sessionList,
      },
    ],
  },
  {
    name: 'log',
    path: 'log',
    component: log,
    redirect: '/home/<USER>/list',
    children: [
      {
        path: 'list',
        component: errorsList,
      },
    ],
  },
];

export default homeRoutes;
