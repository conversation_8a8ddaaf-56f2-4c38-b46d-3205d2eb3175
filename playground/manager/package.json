{"name": "@heimdallr-sdk/manager", "version": "0.0.16", "private": true, "description": "@heimdallr-sdk/manager", "license": "MIT", "author": "<PERSON><PERSON><PERSON>", "scripts": {"dev": "cross-env NODE_ENV=development vite --open", "build:private": "vite build", "serve": "vite preview"}, "homepage": "https://github.com/LuciferHuang/heimdallr-sdk/blob/main/packages/manager/README.md", "repository": {"type": "git", "url": "git+https://github.com/LuciferHuang/heimdallr-sdk.git"}, "bugs": {"url": "https://github.com/LuciferHuang/heimdallr-sdk/issues"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "vue", "manager"], "publishConfig": {"access": "public"}, "dependencies": {"@element-plus/icons-vue": "^2.0.9", "@highlightjs/vue-plugin": "^2.1.0", "axios": "^0.21.4", "echarts": "^5.2.0", "element-plus": "2.2.36", "highlight.js": "^11.9.0", "js-md5": "^0.7.3", "qs": "^6.10.1", "rrweb-player": "^1.0.0-alpha.4", "sortablejs": "^1.14.0", "type-json-mapper": "^1.2.6", "vue": "^3.2.40", "vue-router": "^4.1.6"}, "devDependencies": {"@vitejs/plugin-vue": "^1.6.0", "@vue/compiler-sfc": "^3.2.6", "cross-env": "^7.0.3", "path": "^0.12.7", "sass": "^1.39.0", "sass-loader": "^13.0.2", "typescript": "^4.3.2", "vite": "^2.5.2", "vue-tsc": "^0.2.2"}}